---
date: 20221207
areas: DysonSphereProgram
title: 
description: 
updated: 
---
# Relationships
-  is a [[Components]]

# Ideas
- 202411080547: [My Main Thoughts About Proliferation]

- 202412122230: [What Should Be Sprayed in Late Game?]

- 202411110443: [How Early To Spray? What Should Be Sprayed in Early Game?] My general advice on this is to make a blueprint that has the number of machines needed it was "unsprayed" and include spray coater support in the blueprint. Now you have a single blueprint that has an uneeded coater in it, and running the risk of too many machines if it is being used, the worst of both worlds!

# Notes

## FactorioLab
- [Proliferator mk1 96B](https://factoriolab.github.io/dsp/list?z=eJwtyTELwjAUBOB.k-EGaQSLHd7ge5BiEVFU0EkUQymlFpIq6tDfLmmy3HfH9cQFlsUshwYEmbKEKkSpHJkeHlo54gaTZoAP.CZkA48slFW8h8giwnXyFj2kWSXXweZuSZSzH9rhggdavPBFDt6CT-AzuAU.wXOIGaWE7Ec5qq5zdFVv0voPglgyYA__&v=11)
- [Proliferator 1B](https://factoriolab.github.io/dsp/list?z=eJwtyTELwjAUBOB.k-EWE4tChzf4HrRYRBQVdBLFUEqphaSKOvS3S5os991xPXGOPMtmRmsNAwi0soQqRKkcFT08jHLEDSaLAT7wm5ANPHQoq3gPkUWE6-QtekizSq6Dzd2SKGc.tMMFD7R44YsleAs-gc.gFvwEzyHFKCVkP8pRdZ2jq3qTMX8RtjMa&v=11) (note: there are non-integer assemblers...try those pile sorters sometime...)


## Discussions
- 202502181219 - [Does proliferating proliferator make it proliferate better?](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1is58c3/does_proliferating_proliferator_make_it/) - Spray the spray, as usual. No buildings get spray upgrade except accumulator (technically an item that you can place). Extensive comment that notes that proliferators can only be sprayed once for more charges.
- 202412091602 - [After 250 hours I finally built a blueprint that doesn't feel to janky for a 0.5 resource run(+ it is exactly 150 facilities)](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1hakbcm/after_250_hours_i_finally_built_a_blueprint_that/) - Mall module that is proliferated. Is that even needed?
- ************ - [Proliferation](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1ha6onp/proliferation/) - Question on preference to spray input or output
- 20220126 - [How To Proliferator & Spray Coater 🪐 Dyson Sphere Program 🌌 How To Tutorial Guide](https://www.youtube.com/watch?v=xKKpw8czWUI) - 
- 20220122 - [QUICK GUIDE on Proliferator, Spray Coater & Automatic Piler | How To Tutorial | Dyson Sphere Program](https://www.youtube.com/watch?v=Eo65ijoIpis) -





## Script
Hello viewers, you’re watching Dyson Sphere Program 

where you can expect to see build guides, mechanics, news, theory crafting, and the occasional playthrough (if warranted).

Here we are going to do a very, very in depth analysis of proliferation

That really rewards you for going through the design effort

What makes this mechanic even more in depth to the point of madness is you are given the choice between two kinds of “proliferation”.

As such proliferation is a very complicated system, and not surprisingly is difficult to fully optimize and understand, if ever. 

Let’s go through some of those rules-of-thumb and see if they hold water or not

Rule #1: Never use speed-up

MISLEADING: Just like anything else in this game there is a time and a place for everything. 

First the facts, speed-up proliferated layouts take up less space at the cost of more power. 

Therefore there is little interest in this in the earl game where there is plenty of space and little power. 

In the far end game however, it’s switched around, where there is little space and alot of power.

And we’re not just talking about physical space like space in a planet, but digital space as dictated by your hardware.

In fact for some productions, extra-product would benefit more than speed-up for this particular situation, 

but covering which ones they are is beyond the scope of this video (I told you proliferation is complicated).

Rule #2: Extra-Product if the 

So now our question gets refined to which is more valuable, 50 nanotubes or 800 iron ore?

I’l leave that for you to deceived (after all everyone's situation is different) but hopefully you are left with a better idea of the decision you are trying to make.

Do not be fooled by “infinite” resources. 

Throughput is also a factor. 

Example You can have deuterium in a gas giant but that doesn’t matter if only a few hundred of it can be made in per minute and you are needing several thousand

