---
date: ************
areas: DysonSphereProgram
title: 
description: 
updated: 
---
# Relationships
- ************:  refers to [[Buildings]] that automatically move items in a so called supply-demand item network. There are currently three buildings which are [[Logistics Distributor]], [[Planetary Logistics Station]], and [[Interplanetary Logistics Station]]

# Ideas

# Notes

## YouTube
- 20210125 - [Guide How To Ship items between planets 🤖 Dyson Sphere Program 🤖 Tutorial, New Player guide,](https://www.youtube.com/watch?v=zEorNDAiBtI)
```vid
https://youtu.be/zEorNDAiBtI
Title: Guide How To Ship items between planets 🤖 Dyson Sphere Program 🤖 Tutorial, New Player guide,
Author: JD Plays
Thumbnail: https://i.ytimg.com/vi/zEorNDAiBtI/mqdefault.jpg
AuthorUrl: https://www.youtube.com/@JDPlays
```

- 20211012 - [QUICK guide on LOGISTICS STATIONS | Basics, Priority, Power, Warpers | Dyson Sphere Program](https://www.youtube.com/watch?v=eUbUS1r8VQM)
```vid
https://youtu.be/eUbUS1r8VQM
Title: QUICK guide on LOGISTICS STATIONS | Basics, Priority, Power, Warpers | Dyson Sphere Program
Author: The Dutch Actuary
Thumbnail: https://i.ytimg.com/vi/eUbUS1r8VQM/mqdefault.jpg
AuthorUrl: https://www.youtube.com/@TheDutchActuary
```

- 20210207 - [How To Use Planetary Logistics Station, Using Drones To Move Items - Dyson Sphere Program Tutorials](https://www.youtube.com/watch?v=G1732k6EVk0)
```vid
https://www.youtube.com/watch?v=G1732k6EVk0
Title: How To Use Planetary Logistics Station, Using Drones To Move Items - Dyson Sphere Program Tutorials
Author: MacGhriogair
Thumbnail: https://i.ytimg.com/vi/G1732k6EVk0/mqdefault.jpg
AuthorUrl: https://www.youtube.com/@MacGhriogair
```






## Script
Hello, Welcome to Dyson sphere program 

this is Umabel 

and I will be your guide to everything you ever wanted to know about logistics stations.

There are several “guides” to logistics stations: however…I consider most of them a bit dated, especially with the recent changes:

Dyson Sphere Program is in part a factory and logistics game. You transfer items to factory, then take products out of factory, move it to next factory and so on and so forth.

The defacto mechanic for moving items is logistics stations. 

There are two kinds of stations, the planetary logistics station and the interstellar logistics station.

But most of the time I just run to belt out tattoo done

There are two types of stations, mostly. 

There is the planetary logistics station and then later is an “upgraded” version to the interstellar logistics station. They both have their advantages and disadvantages.

The planetary station is the little brother, small, kinda wimpy. 

But don’t let that fool you, as some jobs are better done by the short guy.

The interstellar logistics station is big guy. 

Strong. Throws vessels across planets and systems. Good. More drones and power.

Then there are the planetary miners and orbital stations. It’s kinda like half-way station in that they can interact with drones and vessels, respectively, but that’s about it.

In the research tree, both the the stations are located in the top portion of the tree. 

In my opinion, unless you are planning to run your starter planet with belt spaghetti, I strongly recommend you beeline your way to these technologies, or even purchase them outright with your metadata to start this process quicker.

A logistics transaction is done between two stations. 

The station that has the item, is called the supply side, and the station taking the item, is called the demand side. 

There are two ways the transaction is done it will either be a push or a pull.

Think of when you order a pizza. 

When you start to order a pizza they ask you: delivery or carry out? 

Assuming you have a car and want to save some money on delivery fees, you order carry out. 

You take your own car, drive to the pizza place, pick up pizza, and drive back home.

Or you don’t have a car, or way too busy placing orbital collectors. 

Then you order delivery. 

The pizza place has its own drivers with their own car bring pizza to you, and then drive back on pizza, their merry way back to the pizza place (please remember to tip).

The same way with push and pull. 

A push transaction the station with the items uses it’s own vessels to bring the items it’s supplying, while a pull transaction it’s the station that’s taking the items sends out it’s vessels. 

It’s all the same in the end, the end the vessel will always come back where it was sent from.

Also note, the station sending the vessel needs to have the energy to do so, while the other station does not. 

So it’s a common strategy to have leave a station unpowered if the power is not available. 

Also the longer the distance the vessel is being sent, the more energy is required to do so.

Most of the knobs and dials are straight forward but there are some slightly confusing or overlooked parts that I’ll mention about here:

Between the storage and the max the In Transit: this will depend on whether a push or pull transaction is being done. 

In a push transaction, the demanding station will show that items are about to pushed to it. 

In a pull transaction it is the same thing but the supplying station will note that items are about to be pulled from it.

For interstellar stations, there are a bit more knobs to look at.

Transport range of vessels this is the max range that a station can send it’s own vessels. 

If everything is outside of its range, it will act like a unpowered station, not sending any of it’s vessels. 

It can still be pushed or pulled by other stations however…

Distance to enable warp: This the minimum distance that vessels will use warpers to speed up their trip. 

This is particularly useful for intrasystem travel where planets can be too far and vessels would take too long.

Orbital collector. 

When check the station will pull from orbital collectors on gas giants. 

This is in addition to other supply stations. 

When this is unchecked, it will only pull from interstellar stations. 

Note again (sadly) it can still be pushed by other stations. 

There is actually a max rate of item transfer to consider for a single station. 

No matter how fast the vessels and drones can be, there is a maximum number of transactions that a single station can do.

For planetary stations it can only bring in 

While for interstellar stations is can only bring in.

Under the right circumstances a drone slash ship can be used on both legs of the round trip

There are one mod that I would absolutely recommend at the very least finishing your first mission completed. 

That would be LSTM by Heitima. 

In fact I recommend this as your first mod ever but that’s another story.







## Reddit
- 202412180446 - Video - [My Favorite Tech to Complete](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1hgy9at/my_favorite_tech_to_complete/) - OP is referring to [Logistics Carrier Capacity] when researched, around 60 or so Insterstellar Logistics stations now have increased capacity, and simultaneously send off vessels to fill the orders.
- 202406271542 - [Exclusive pairing for ILS? How does it work exactly?](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1dq05lf/exclusive_pairing_for_ils_how_does_it_work_exactly/)