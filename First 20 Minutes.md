---
areas: DysonSphereProgram
date: '202306291433'
title: 
description: 
updated: 
---
Greetings and welcome back, fellow star builders! Whether you're tuning into the Dyson Sphere Program or the Dysons Fair Program, the excitement is palpable. It's yours truly, your resident DSP enthusiast, back on the game after two years, eager to share my thoughts, strategies, and interstellar experiences with you all.

I know, it's been several light years - I mean, months, since my last video. But fear not, I've got a cosmic array of exciting topics lined up, fuelled by some incredibly inspiring early gameplay videos I've stumbled upon lately. So, I thought, why not share a constellation of my own early game ideas?

So, strap in, because we're about to launch a new series that's part gameplay, part let's play, and a dash of tutorial content. I won't promise it's going to be a full-blown tutorial, but I hope it will tickle your fancy, whether you're here for the practical tips, the entertainment, or a delightful mix of both.

I'll be piloting this series as far as I can, at least until the next major update lands like a comet, dazzling me with its enticing new features. Then, it's on to the next cosmic adventure. But for now, let's not waste any more starlight. Get ready to warp into it!

So starting a new game. If you are observant…like I’ve been playing this game for quite a bit. Anyways, what kind of seed I’m going for here. The main important feature here in my opinion is going to be the star spacing and rares. I can’t see the rares not all of them anyway but the spacing looks very good here.

Is this a speedrun…not not really…though it may seem like a speed run I make many deliberate anti-speed run decisions. Heck even there is not even 2nd moon lying close around. Though I do want to avoid excessive do-nothing periods nobody enjoys seeing that.

Starting with coils and boards and now gears will makeup our 4 item bus. Basically a bus in the context of this factory gameis group of belts being routed together. Here we’re starting with

The first two factories actually producing buildings will be for the belt and the sorter

Let’s redo the magnet belt, that should come from the

So yeah the kind of starting base I’m trying to develop is a mall / early science sort of thing. It will produce most of the buildings needed at the required rate and in the off chance no buildings are being made, the underlying production would go into research…it will be small but it will help a bit hopefully.

And it will all run off wind turbines? I’m not sure…but it’s that’s going to happen I’m going to have to make this base happen at around 1 GW of power. The base is around 5 MW I think but the power consumption will add up considerably. Hopefully no more than around…1 GW.

But heck…I think it would look cool to have a self powered starter base powered with wind turbines that can carry on forever, that sounds pretty cool.

So those coils and boards are doing pretty well…so I might as well slam down that free matrix lab. The lab will take those boards and coils and turn them into electromagnetic matrices…also known as blue cubes. Of course the way it works is then the blue cubes have to be burned for the research…matrix labs can also do that which I don’t have at the moment so I need to remember to unload that one matrix lab everyone once in a while.

You may have noticed the placement of the first assemblers at this point. First question, why the gap between the gears and belts. Honestly, I’m not sure…I’m thinking maybe I can add more assemblers before the belt factory. It’s mostly going to be expand boards and coils. Just like the smelters i know for certain I’m going to need more them sooner rather than later.



I’m thinking of an idea where there still will be two columns of magnet and iron ingots…but then the outputs (there are two of them) will just meet up right in the middle go straight up and then split up when it reaches the assemblers. I might try that out when I get to it.

Two critical buildings I still need (well…always going to need) will be assemblers and smelters which are not automated yet…so for the time being I’m pulling the materials straight out the belt. Many players would use put storage boxes in the assembly line to hold items needed for crafting. I don’t do that. I basically convinced myself that I am beyond needing boxes to store stuff. 




Now that second assembler making sorters…perhaps the priority should have been to making wind turbines instead? I feel that sorters are actually easier to replicate and the sooner the power generation is ramped up, the sooner production will get back to 100 percent.

Now is a good time to mention the wind turbine spacing…the windturbines are all spaced 10 grid apart that way a pair of assemblers can fit between them and just as cool a pair of boxes can be placed between the wind turbines as well. This layout rule as worked out so well it’s one of the inspirations for this base building process.



That was alot of building and I’m noticing that I’m running out of energy. I think I’m still running on coal but despite that I’m still running out. That’s where I think having wireless power unlocked asap will be helpful as I’m basically prioritizing the limited energy to getting the buildings out as soon as possible.


Not quite sure why I’m doing this now, but miner #4 is going to for mining stone. The stone in turn get smelted into glass and stone which is actually for the another set of buildings but then again it’s nice to have those items being available.

Another mistake that I’m doing is that I’m building on two fronts. I was building on one spot and then all of a sudden I got distracted and went over to another spot to build. Now my construction drones are confused on what to be focusing on. And I believe that when construction drones have to go further to build, they do deplete energy faster, so I need to keep in mind to focus on my construction better.

What I also like about the 10 grid wind turbine spacing is that it is very easy to find the spots to put them down on. If I was trying the minimum 9 grid spacing I would either be carefully moving the wind turbine, using the spacing adjustment, or using blueprints or any of that complicated mess. Here I just find that spot to put it down and down.

Really looking forward to how I’m going to get the next set of buildings automated. Don’t get me wrong I’ve done it before…but with this particular rule set things can get interesting.

It’s a small shame that things have slowed down a bit here. Despite a full supply of items being produced, barely much of it is being used. This is one of the reasons buffer boxes are used so at situations like this the items can still be made for a bit longer until the demand is ramped back up. But not that I can tell immediately just by looking the belts that I’m full. I don’t think storage boxes give you that quick look at how things are going, not as conveniently I think. Anyways another reason I don’t use boxes in that way.

And usually I try to avoid the spots where a wind turbine would go. I’m not sure how well i can keep that up in the middle game but we’ll see how it goes.

Another perk is that the wind turbines are spread out more so that along with the easier placement, means

I think I’ll wrap that up here. Again, I have some trickle blue cube production and just as important are 6 buildings being produced. This is only the start of the problems, but I’m more than happy to find and take them out. I hope you guys find this enjoyable and I will see in the next one. 










So starting a new game, if you're observant, like I've been playing this game for a bit. Anyways see what I'm going for here. The main important feature here in my opinion's going to be star spacing and rares.

I can't really see the rares that well, but the spacing looks pretty darn good. So starting the game, I immediately flip north side facing upwards and start going through the research tree to unlock the usual buildings used for the start. Afterwards, things gets a little bit iffy, but what I go for is to try to unlock upgrades that increases.

ICARs is build speed and movement speed. Hence that last research on the end of the queue there. The high efficiency plasma control unlocks a wireless power tower that will charge s's energy, which usually runs out pretty quickly when building a lot in the start. Also, very early game.

Irisy replicator is actually a thing. So as I'm mining stone iron, copper ore, I immediately go towards the replicator and having it turn those, it turns those things into items that are used for later and they likely probably will be used for later or collecting automation. Is this a speed run? No, not really.

It may seem like a speed run. I am making many deliberate decisions that does not do that. Heck, even the seat is wrong, though I do want to try to avoid excessive, do nothing periods, nobody really enjoys watching that. The manual research queue is telling me I have 10 magnetic coils to make. So yeah, the sooner I make them the better.

So as those coils are being made wireless, I'll keep it iis busy with mining some copper ore, trees, whatnot. And then every once in a while I'll check that the replicator is keeping itself busy. That's the typical cycle right now, at least until buildings are more buildings are available. And then the game changes once again.

So after the coils are made, then I'll just have the replicator make some gears, and then some, maybe some stone bricks, whatever. And at this point, the smelters are available, which are basically the first tier fa factories available. That means one less item for the replicator to be making, unless, of course I get desperate.

As of this, writing iron ore only turns into two items. Iron in gets and magnets. So already there are two smelters trickling down those two items. And then I have a third smelter available that will be used to make a copper ing. Gets note that in my factory speak I tend to mix up around the units a lot.

For example, when I said iron inge there, I'm actually referring to the factory that's making the iron ingots. Anyway, I'm back at the copper Ora patch. As it's mining, I'm trying to, figure out a strategy to getting a second mine down. But of course, I'm also hammered by the fact that the power needs to be good as well.

And of course, the main source of power at this stage of the game is to use wind turbines. One rule that I'm trying to follow at this point is to place wind turbines at specific spots on the grid. This particular role that I'm trying to do will hopefully solve more problems than it creates. But for now what I'd really like to do is get that second miner down for the copper or, but sometimes placing down wind turbines is a priority, so I understand.

So I'm thinking I should probably just put down a third or fourth or a wind turbine before I get the second miner down, which is only fair. Or no, maybe put down the second miner first. After all, production facility, that tends to be my unusual thinking here within the power coverage area. I'm just about halfway through completing a certain task and then all of a sudden I find myself doing another task, which is also fair depending on how you look at it.

Now, I put down the second miner and then I realize, am I making anything right now? Definitely need 10 circuit boards at this point. Awesome that next research on the queue should unlock conveyor belts and sorters. It's a shame that I don't have conveyor belts or sorters available yet, but, oh, what can you do?

That's why I'll keep on using the good old replicator and interacting with the smelters manually to keep them working, which is to be fair, pretty viable strategy. It should have enough materials to last for a while now, finishing up the circuit boards. And as usual, adding more stuff into the queue.

Ideally, I'd like to keep the replicated running for 30 seconds, but it's at this point I can't even keep up from the looks of it. I ran out of stones, so I'm gonna fetch some stone chunks before I get to work with the conveyor belts. You have received the story? Yes. You can hear your belt. Which can achieve full automation.

So naturally, if I have belts, I should be going down to the iron smelters and hooking them up with wear belts for automatic smelting. It looks like I am not doing that. Instead I am just making sure that the smelters are still running, doing their thing and laying down a couple more wind turbine power, because why not?

One of the reasons I distracted myself a little bit and placed down with turbines is that if the power went bad, then that will decrease the effectiveness of all the buildings. This especially includes output rate. So basically I can't have one without the other. I have to place down factories as well as the wind turbine that is generating the power for it.

Now, as I'm planning along here, I should probably note two things. One is that I'm going to start the factory at a very small scale. One example that you can see here is that there's only one smelter for the iron in get and one smelter for the magnet. Am I going to need more later on? Probably. The thing is I'm going to spend the time that I have to get something small going first, and then once things gets going, I can later on upgrade as I see fit.

And the second is I have no intention of burning coal for electricity. The reason for that is, of course, I want to try using mass wind turbines first. And of course that leaves more coal leftover for more more spraying, which I'll get into later. But speaking of coal, it looks like I'm going to need my third minor down to generate coal for me.

Even though I am still not burning coal for electricity, I still need coal as a personal fuel storage. So here, I'll just place down the number three miner and a storage box to increase the amount of coal on hand. So after grabbing a little bit of coal to keep IOUs going, I'll just leave it alone for a little bit, and when I come back, there'll be a whole wad of coal waiting for me.

So yeah, those two things. No coal power plants wind turbines only. So fumbling a little bit with the coal here. It probably would've been a good idea to have thunders earlier. So now I'm just twiddling my thumbs here. So I put down a second sorter here, FYI four mark four sorters should transfer over a full mark one belt.

So I'll just leave that alone and check out how the copper mine is doing. Of course, I'll just pick up some items as usual along the way. And clearly this copper Inge smelter needs some automation in its life. Basically, I'll just have the ore come from the top and finish up at the bottom, and eventually, as the copper Inge is expanded, the belt holding the copper INGOs will still exit out at the same places as before.

Yes, I think that'll work out quite nicely and I keep running into that lack of building problem as always. Anyways, that should be taken care of pretty quickly as soon as the assembler research is taken care of. So I think what I'll do to prepare for that is to extend out the inputs, the main ones being the copper, iron and magnet, starting with the copper.

I'll just extend the belts out towards what, I guess I'll call the assembly line and I'll just keep the replicator busy. Now, of course, another option is to just get the assemblers going immediately and just, manually loading them. But it is what it is. If I'm putting belts out first, that's okay.

And if assemblers come out first, then that's okay too, whatever happens. But of course I should mention since the copper belt is out now, then the pro is definitely going to be, I don't have to worry about, loading copper. Okay, so with the research, get a free assembler, and I think it'll start making me coils first view mode.

And again, since it's right there on the next to the copper belt, it'll be fed automatically immediately. But I keep getting the feeling I'm gonna be putting off on magnets for a while. Ironing gets, is gonna be pretty important, but the first item I'm gonna have being used for is to make boards for me.

And that's a bit of a yikes stare. The ironing, get belts too far away from the assembler. Basically that means a sorter can't fit in there. So it's either jam a sorter somehow into that assembler or just redo the belt. I think I'll do a little bit of a compromise here. I'll just jam the belt instead. I think I'm gonna plan for the long haul for a little bit.

I'll keep the replicator busy for a couple seconds. Now we just need to get it powered and set the recipe. And now we have both the coils and boards being produced. Next item business is to get the belts to carry out the coils and boards. Again, I could have focused on just getting assemblers out first, which I'm actually working on right now.

But while I'm twiddling you around, I can might as well just lay down a couple of belts. A few things going on, but right now a few of the missing things right now is getting a belt of magnets for the coils and getting output belts for the coils and boards, and also need to fetch the next batch of coal very soon.

Mission accomplished. I've officially started coils and boards for the assembly line, and before I begin the rest of the factory, now would be a good time to stop by for some coal. I'm not sure if I mentioned this already, but as I go through the game, what I'm thinking is that I will try to, oh dear. I stopped here, got that wad of coal just in a nick of time.

So yeah, the kind of starting base I'm trying to develop is gonna be like a mall early science sort of thing. It's gonna produce most of the buildings needed at the usual rate required. And in the off chance that no buildings are being made the underlining production will carry out forward and just, go into research.

It'll be a small amount, but it'll help out anyway and it will run on wind turbines. I'm not sure if we can make that happen, but probably a base, large base it's gonna take around about a gigawatt of power. Right now the base is five megawatts, but it will, it'll ramp up quite conservatively.

Hopefully no more than one gigawatt, but heck, I think it would be cool. I have a self-powered starter base with wind turbines, carrying on forever. No, looks and sounds pretty cool. Not sure it'll happen. We'll see. Barely getting the iron and gets and gears started, but those coils and boards are doing pretty well, so I might as well slam down that matrix lab pretty early into the line.

The lab will take those boards and coils and turn 'em into the electromagnetic matrix also known as the blue cubes. Of course, the way it works is then the blue cubes would have to be burned for the research. Matrixx slab can also automatically burn the blue cubes for research. But since I don't have one set up just yet, I have to remember to offload the blue cubes every once in a while.

You may have noticed the placement of the first assemblers at this point, Yeah. First question. Why is there a gap between the gear and the belt? Honestly, I'm not sure. I'm thinking maybe I can add more assemblers. Before the belt factory it's mostly gonna be expanding for, the bores and the coils.

I, I know just for the, like the smelters, I know I'm going to need more of them sooner rather than later. There's another reason, but I'll get into that later. For now, what I'm trying to do is try to get the coils and boards automatically fed. I'm just throwing down a belt for magnets, I guess for no, no other reason.

Sometimes I just throw a belt down to see what it's gonna look like. Hopefully a fruitful exercise, but it looks like it's gonna pay off. The magnets will and the coils it's gonna feed, will just keep going onto the left and the ironing gets, will keep going to feeding the boards to the right. Now, of course, if I can find a way to, finish the magnet belt, that would be swell.

It's gonna work, but it's not looking particularly good right now. I'm thinking of an idea where there will still be two columns of magnets and iron in gets but then like at the top and towards the top there those two outputs will just meet up together and middle go straight up.

And then when it reaches the assemblers it will, it'll split out again. I might try that out if I get to it. Two critical buildings, I. Still need, always gonna need assemblers and smelters not automated yet. So for the time being, I was just pulling items off of the belt. Many players would just put storage boxes in their assembly line and hold, items that they need for crafting.

I don't do that. I basically convinced myself that I'm beyond eating boxes at. Hopefully I'll not pull too many items out from Besson look like a, dumb bite. I'll just expand the coils and the boards a little bit. I'm thinking about that second assembler there. It's making sorters, but what if instead it was making wind turbines instead?

I feel as though I can easily make sorts with a replicator. Wind turbines probably a little bit harder, and the power generation is being over tapped quite often. Having a small handful of wind turbines being made earlier would've been helpful. Right now I'm gonna have to settle with wind turbines and Tesla towers being made on this second pair.

And on the last pair of factories they'll be making miners and assemblers.

Now's a good time to mention the wind turbin spacing. The wind turbines are all spaced 10 grid apart, so that way a pair of assemblers can fit between them. But also storage boxes can fit between the wind turbines too. So just imagine a starting to intermediate base with wind turbines, boxes, and assemblers all mixed together.

It sounds interesting and that layout role is one of the inspirations towards this towards space building.

It's a lot of building so far and I'm running out of energy. I think I'm still running on coal or else I'd be really slow, but still running low. That's where I think having wireless power towers unlocked ASAP will be helpful as I'm, basically prioritizing the power towards charging and building stuff faster.

Which is basically another reason I should have gotten wind turbines out sooner. Also, researching drive engine. Drive engine one. It's a technology that helps ous fly practically useless right now since there's no power to fly with more. Oh not a huge deal. We're probably only talking like a minute or two here.

It'll all end up the same either way. And hopefully that last order for the wind turbines will come in and wind turbines will be made automatically soon.

I'm not quite sure why I'm doing this now, but yeah, I think I'm gonna put Meyer four here for mining stone. So the stone's gonna get turned into, glass and stone, which is for another set of buildings. But then again, it's nice to have those items being available. It's a small shame that things have slowed down a little bit here.

Despite full supply of items being produced, barely much of it is being used. This might be another reason. One of the buffer boxes are being used at situations like this, so at least items are still going somewhere until the demand is ramped back up again. But note that, looking at the belts right now, I can immediately tell.

N just by looking at them that they're full and therefore the demand's a little bit on the low side. I don't think putting storage boxes would give you that quick look of, how things are going. Not as conveniently, I don't think. Anyways, another reason I don't use storage boxes in that way says the guy who's making storage boxes, And then finally putting down the two smelters that will be making glass and stone.

Again, the timing's a little bit off. I don't really need this right now, which might be a little bit of a mistake on my part. Another mistake that I'm doing is that I'm building on two fronts. I was building on one spot and then all of a sudden I got distracted and went over to another spot to build.

Now my construction drones are confused on what to be focusing on, and I believe that when construction drones have to go further to build they do deplete energy faster. So I need to keep in mind to focus on my construction a bit better, and then I'll try to hook up the last two assemblers.

Overall, I would say finishing the start of this assembly lines a little bit in the slow side, Sometimes you're gonna be faster, and other times you're gonna be slower.

Now with wireless power towers being unlocked, I immediately start making wireless power towers. And once that building is made, I can start charging up really quickly. You built a wireless powered tower. I definitely see myself using, using these buildings very often. And for putting research in the queue, I think I'll improve the building speed.

So I'll add in communication control and drone engine.

What I also like about the 10 grid wind turbine spacing is that it's very easy to see the spots to put them down on. If it was like the nine minimum grid spacing, then I would, I either be carefully moving the wind turbine. To when it's just at the right range or using that space adjustment thingy or using blueprints or any of that, complicated mess.

Now here I just, find that spot, then just put it down and usually I try to avoid the spots where the wind turbine would go. I'm not sure how well I can keep that up in mid-game, but we'll see how it goes. Oh, another perk is that the wind turbines are basically spread out more, just a little bit on along.

That, along with the easier placement means that it's much easier to spread the base out. The starter base is making six buildings right now, so naturally I'm going to need six boxes to go with that. So what I don't like is that there's some water over there where I would like to put a box. So for now, I'm just gonna have to settle putting it up instead of down with the rest of them, that said, I think I have another goal I'm gonna fill this entire planet with with foundation.

Just maybe a little small little lake lying out somewhere, but everything else is just covered, completely decimated. Nothing left. Except for production, of course, I think now is a good time to expand my iron and gets in magnets. I've just recharged for a little bit while I figure out how to do that.

I'm going to eventually need six melters of each item here. Six Melters will take one, one belt of iron or, and turn that into the product. Actually for the magnets, it's a slightly bit more, but you get the idea. I, here are my options. Either, redo the whole thing or just slightly expanded it a little bit.

I think I'm going to go for the slight expansion for now, and I think I'll wrap that up here. Again, looks like I have some trickle blue Cube production and just as important, those six. Starting buildings being produced. I'm not gonna lie, that's only the start of the problems, but more than happy to be taking them out.

I hope you guys find us enjoyable and I'll see you in the next one. Peace.