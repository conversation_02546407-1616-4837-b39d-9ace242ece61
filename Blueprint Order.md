---
areas: DysonSphereProgram
date: '202308251114'
title: 
description: 
updated: 
---
To stand a chance of scaling up blueprints are a must, and well organized blueprints are much quicker to find, easier to use, and frankly makes end-game more fun. 

With time and practice, you’ll find your unique style of organizing blueprints.

And perhaps you might be interested in ordering the blueprints a certain way…alphabetical order, least amount to most amount, most favorite to least favorite, you know whatever

Currently, the game doesn’t allow you to order them off the bat in-game, strangely enough you can go to the file explorer (also by clicking on the button in-game)

And rename the blueprint file names based on the order you want. What I’m doing here is prefixing each of the filenames with a double digit number, 01, 02, and so on

And from then on the game will quite oddly read those filenames and order them accordingly