# Post
- 202506140737 - [LPT: If you are setting up an ILS/PLS to produce a good, set that good to the top slot.](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1lb6mqt/)

# Messages
- 202506140756 - [<PERSON><PERSON><PERSON>] - I did the same for all my blueprints.
- 202506140854 - [sumquy] - no. the the inputs go in the first slots, then the output, and warpers in the last slot.
- 202506141103 - [<PERSON><PERSON><PERSON>] - The number of inputs is variable and there is usually only one output, so putting the output first makes it clear at a glance while putting inputs first the output might be in the third or fourth position. - (reply: 202506140854)
- 202506140919 - [Pakspul] - This, or input, output, proliferation, warpers. If three or more input, then proliferation from outside (storage) - (reply: 202506140854)
- 202506141400 - [ecmrush] - You don't need to waste slots with warpers on every ILS though; you just need to import them to once ILS and distribute them to all the others via logistic bots. Same deal with proliferators. - (reply: 202506140854)
- 202506141009 - [xSorry_Not_Sorry] - I’ll die on this hill. - (reply: 202506140854)
- 202506141611 - [SugarRoll21] - I do "input, proliferator, output" with inputs being the top ones - (reply: 202506140854)
- 202506141342 - [ChinaShopBully] - Top: output\nSecond: Most used input\nThird: Next most used input\nFourth: Third most used input\nFifth: Fourth most used input or warpers
- 202506141056 - [Krinberry] - I just name my stations so it's clear from the name what they're there for. :)
- 202506142100 - [dodexahedron] - Man I name EVERYTHING. Stations, stars, planets.... Anything worth tracking individually.\nScrew the star and planet names. This star isbig-ass blue-ball, the outer planet isTI+Si ->, its moon is->Fe | Acid+Concrete->, and the inner planet practically inside the star isDuracell 1(tidal locked, covered with solar panels, and has batteries on a revolving door). - (reply: 202506141056)
- 202506141635 - [CaptainKyleGames] - That's how I organize all of mine. It makes the most sense in the control panel view.
- 202506141017 - [jeefsiebs] - Further: make blueprints for “ILS with 2 ingredients”, “ILS with 3..”\nTop slot is output. Blueprint is set up with belts coming out filtered by slot number (you can filter to “#2” without specifying the ingredient). Slot 5 for me is always proliferator unless it’s a 4 ingredient.\nFor any material produced by assemblers, just drop down blueprints, set 1,2,3 to output,ingredient,ingredient and it just works. Just have to set assembler recipes
- 202506142130 - [theschadowknows] - That’s a really good idea.
- 202506151404 - [GA70ratt] - I do warpers the first slot because I need them the most. It catches my eye first when I check inventory.

