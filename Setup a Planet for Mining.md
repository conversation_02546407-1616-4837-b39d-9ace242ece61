---
date: 202112130149
areas: DysonSphereProgram
title: 
description: 
updated: 
---
# Relationships
-  is a [[Problem]] where a planet is landed, and developed solely for the purpose of exporting it's raw resources. (2021121301450)



# Notes

## Reddit
- 202412261947 - [How Do You Calculate PLS Rate Of Aquiring Resource](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1hn2ql8/how_do_you_calculate_pls_rate_of_aquiring_resource/)
- 202308030349 - [What is your preferred way of handling Mining colonies?](https://www.reddit.com/r/Dyson_Sphere_Program/comments/15gxj5n/what_is_your_preferred_way_of_handling_mining/) - I put my own answer in here btw


## YouTube


# Log
- 202412270909: [Objective of Planet Mining] The objective of planet mining is to quickly and efficiently setup a system for mining resources out of planet, usually away from home system

- 202412270910: [Planet Mining Background] Toward the mid game, some resources will be depleted. Usually iron, but it will depend on the seed and what is being built. Other planets in the system can be tapped, but not too long after, the home system will be depleted and there will be need to tap other systems.

- 202412270911: [My Personal Method to Planet Mining] My preferred way (once on the planet) is to belt ore to ILS with monitors. (1) Go to a planet with an ore type that I want. (2) Adv miner all that ore type. (3) Drone them all to a PLS. (4) Belt the ore from a PLS to an ILS. (5) Place a traffic monitor on one those belts, set to alarm at "fail and no cargo". (6) Repeat for other ore types and/or scale up. (7) When the ore runs out, I will be alerted to do something about it, usually dismantling the mining and setting up a new mine elsewhere. (8) This monitored setup is similar to Nilaus's cross-dock idea, but it's more tailored for a locally produced ore.


