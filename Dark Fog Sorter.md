---
date: 202503112333
areas: DysonSphereProgram
title: 
description: 
updated: 
---
# Relationships
-  is a group of buildings that sorts items in a [[Dark Fog Drops and Farming]]
- what is [This]
# Output
## 2x9-Item Dark Fog Sorter. Unjammable. 
Technologies and Upgrades Needed:

## 4x15-Item Dark Fog Sorter. Unjammable. All 60 Items. 448 Facilities.
- In my one-planet challenge with maxed out dark fog, I got sick of my sushi belt dark fog sorter jamming all the time, so I dabbled with unjammable sorters where everything goes through filtered battlefield analysis bases (BABs) and filtered sorters. All 60 items are sorted out to their boxes. If any of the boxes get full, everything else is still going.
- Most of the designs I have seen in this category have been using too few (like 1 or 2) BABs and/or what I thought was alot of belts to take them all to a long line of boxes. By using 4 BABs, and little bit of the "Good Ole' Box Methodology", there's ~25% (or even less) belts used.
- Each of the four modules take up 112 facilities for a total of 448 facilities so this can be planted down with Mass Construction 3. 
- It is easy enough to divide the design into 2 or 4 modules for earlier use and/or reshaping.
- Everything is presented here at lowest tier. Upgrade sorters and belts depending on incoming rates. Add and configure BABs (or not) and turrets depending on the situation.
- Technologies and Upgrades Needed:
	- Battlefield Analysis Base
	- Vertical Construction 1
	- Mass Construction 3 (or divide up into modules for Mass Construction 1 or 2)
- Blueprint: https://www.dysonsphereblueprints.com/blueprints/factory-4x15-item-dark-fog-sorter-unjammable-all-60-items-448-facilities
### Comments
- Total 

## Dark Fog Sorter Hub (PLS)
- Collector Hub used to collect and store your conjunction your BAB dark fog sorter(s). An incomplete example here [4x15-Item Dark Fog Sorter. Unjammable. All 60 Items. 448 Facilities](https://www.dysonsphereblueprints.com/blueprints/factory-4x15-item-dark-fog-sorter-unjammable-all-60-items-448-facilities)

## Comments

- 202503120758 - [MonsieurVagabond](https://www.reddit.com/user/MonsieurVagabond/): 
	- Nice work ! Why not directly put logistic distributor on it for easier dispatch ?
	- I use 2 main BP, [one that directly filter all 45 item](https://www.dysonsphereblueprints.com/blueprints/factory-bab-dark-fog-drop-sorting-facility) you need for mall, [and one "void"](https://www.dysonsphereblueprints.com/blueprints/factory-bab-sorting-system-12-1-df-farm) where i just setup what i need, and everything goes directly to bots network ( not PLS/drone because that way i can easily prioritize the use of dropped item over crafted one )
	- How is the one planet challenge going ? I did one myself and im curious to know how other fair on it ( titanium is a pain )



# Ideas

# Notes
## Blueprints
- 20250308 - [## Complete Polar Dark Fog Farm by alphazulu489er](https://www.dysonsphereblueprints.com/blueprints/factory-complete-polar-dark-fog-farm)
- 20240713 - [## BaB sorting system 12+1 DF farm by mrvagabond](https://www.dysonsphereblueprints.com/blueprints/factory-bab-sorting-system-12-1-df-farm) - this completes mrvagabond's dark fog sorter unjammable solution
- 20240714 - [## BaB Dark Fog Drop sorting facility by mrvagabond](https://www.dysonsphereblueprints.com/blueprints/factory-bab-dark-fog-drop-sorting-facility)


## Reddit
- 202503120135 - [# 4x15-Item Dark Fog Sorter. Unjammable. All 60 Items. 448 Facilities.](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1j9ccas/4x15item_dark_fog_sorter_unjammable_all_60_items/)
- 202502041815 - [# I created a modular dark fog farm for midgame that collects almost all items, has no item upkeep and does not stop working when one item reaches capacity](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1ihvkji/i_created_a_modular_dark_fog_farm_for_midgame/)
- 202408200526 - [# Collect All BAB](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1ewr61o/collect_all_bab/)
- 202402202345 - [# My fog farm. It sorts, it ships, it requests almost all of its parts, it replaces broken laser towers and the explode one (forget what it's called), and it has power built in (suns requesting black and yellow rods, prioritizing yellows). Please comment and let me know if you use it!](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1aw3c15/my_fog_farm_it_sorts_it_ships_it_requests_almost/)
## Log
- 202503130841: Concluding the post I made (202503120135) there were comments about missing automations (I implied they had to be added), questions of how I was doing with my run, and links to other peoples blueprints in the category.
- 202503120950: I don't think I'm ever going to finish my one planet challenge video. So might as well start a reddit presence by releasing my Dark Fog Sorter Blueprint.



