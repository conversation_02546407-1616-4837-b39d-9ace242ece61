---
date: 202503091606
areas: 
title: 
description: 
updated: 
---
# Relationships

# Ideas
- 202503091606: [Plotting out the Resource Locations in a Planet] In order to systematically plot out any planet scale factory, it will be great to plot out the location of each of the resource nodes, amounts, and most importantly the locations. The game is able to get all of this information and mods certainly make this possible, but is there a way to do this from vanilla? 

# Prompt


# Notes

## Log
- 202503091623: (from 202503091622) For starters, I can divide the latitudes into three bands, the south, middle, and north. many of nodes are in the center, with a few nodes being in the north and south. Also if a "relative" coordinate is being used, I would prefer they started wherever the game starts. 

- 202503091622: (from 202503091620) Even the "simple" blueprints are unreadable with vanilla LLM. They are just mk1 belts plotted at different locations in the planet. My goal for now is to somehow extract these locations to a human readable format...and programs to extract the data is just reaching into modding territory which I prefer not to do for a vanilla game. What kind of system would be best of manual entry?

- 202503091620: There could be a blueprint system for jotting down the resource node locations. I gave a blueprint, aka a text file written in a format for the game Dyson Sphere Program. Can a LLM model read this? If not what kind of "known" blueprint would train it to tell what is the blueprint is saying?




