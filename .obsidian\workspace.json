{"main": {"id": "022f6c1860c2c6a1", "type": "split", "children": [{"id": "3cda96bfe4a52067", "type": "tabs", "children": [{"id": "f7d08f65da639ad6", "type": "leaf", "state": {"type": "empty", "state": {}, "icon": "lucide-file", "title": "New tab"}}]}], "direction": "vertical"}, "left": {"id": "7c6e1f856fd1a13a", "type": "split", "children": [{"id": "db0eb0c13f93cbb0", "type": "tabs", "children": [{"id": "ff6b02a07e3e56ae", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "54a691b68b05162a", "type": "leaf", "state": {"type": "search", "state": {"query": "[\"areas\":Prod", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "9fd82ecf9f90b12b", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 295.5}, "right": {"id": "b4ac543a2277aa7a", "type": "split", "children": [{"id": "f52e4217b761c3ca", "type": "tabs", "children": [{"id": "972acc53c6e50703", "type": "leaf", "state": {"type": "backlink", "state": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks"}}, {"id": "f047006a6cab8df0", "type": "leaf", "state": {"type": "outgoing-link", "state": {"linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links"}}, {"id": "2203d1e77155a873", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "7f3e6511a04280a2", "type": "leaf", "state": {"type": "outline", "state": {"followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false, "templater-obsidian:Templater": false, "obsidian-excalidraw-plugin:New drawing": false, "obsidian-shellcommands:Shell commands: Custom variables": false}}, "active": "f7d08f65da639ad6", "lastOpenFiles": ["Universe Matrix Black Box.md", "Buildings.md", "DSP - Technologies and Upgrades.md", "upgrades", "technologies", "buildings/Planetary Shield Generator.md", "Signal Tower.md", "buildings/Jammer Tower.md", "Battlefield Analysis Base.md", "buildings/SR Plasma Turret.md", "buildings/Plasma Turret.md", "buildings/Laser Turret.md", "buildings/Implosion Cannon.md", "buildings/Missile Turret.md", "buildings/Guass Turret.md", "buildings/Vertical Launching Silo.md", "buildings/EM-Rail Ejector.md", "buildings/Self-evolution Lab.md", "Matrix Lab.md", "buildings/Re-composing Assembler.md", "buildings/Assembling Machine Mk3.md", "buildings/Assembling Machine Mk2.md", "buildings/Assembling Machine Mk1.md", "buildings/Plane Smelter.md", "buildings/Arc Smelter.md", "buildings/Miniature Particle Collider.md", "buildings/Quantum Chemical Plant.md", "buildings/Chemical Plant.md", "buildinjg", "build", "buildings", "New folder", "items", "dataview", "excalidraw/Mall System.excalidraw", "templates"]}