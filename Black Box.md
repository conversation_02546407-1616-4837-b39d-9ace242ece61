---
areas:
  - DysonSphereProgram
date: 202307131434
title: 
description: a build style where multiple productions steps are integrated together in one layout
updated:
---
# Relationships
- is a [[Layout Style]]



# Reddit
- 202406120112 - [2 Strange Annihilation Rods per Sec Blackbox](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1ddytwu/)
- 202406190417 - [10 Graviton Lenses Per Second Blackbox](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1dje008/)
- 202406221945 - [20 White Science Per Second Blackbox](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1dm8wp6/)
- 202306300748 - [Where is the sweet spot between black box builds, and importing assembled components?](https://www.reddit.com/r/Dyson_Sphere_Program/comments/14my76s/)

# Log
- 202506211933 - [Blackbox Builds and Transport Costs] If you think moving millions of ore by vessels are cheaper or just as good as drones then by all means black box is the best if not a great way to go. As you mentioned you don’t like running into bottlenecks. That’s a good reason black boxes are so highly preferred.

- 202506211932 - [Hybrid When Drones Win] If drones are cheaper, shifting toward a hybrid model looks attractive. The sweet spot becomes whatever maximizes drone use and minimizes vessel use—for example, producing particle containers (advanced recipe) on black-hole or neutron-star worlds where unipolar magnets are mined locally without vessels.

- 202506211935 - [Assembled Components Defined] Assembled components are mid-tier intermediates used in higher-tier recipes. Importing them can shrink individual builds, simplify layouts, and let you centralize production of widely-used parts.
    
- 202506211936 - [Common Imported Components] Casimir crystals, green motors, yellow chips, titanium alloy, and similar intermediates are frequently imported by players looking to balance self-sufficiency with logistical efficiency.

- 202506211935 - [Choke Point Items in Production] Popular "assembled components" for import might include Casimir crystals, green motors, yellow chips, and Titanium Alloy, among others. These components are widely split off into various production lines and can be more efficiently produced in dedicated setups. The players who prefer this approach are those who want to balance between self-containment of black boxes and the efficiency of centralized production. particle containers (using the advanced recipe) in blackhole/neutron star planets, since the unipolar magnet is accessed directly without vessels.
    
- 202506211937 - [Finding the Sweet Spot] The "sweet spot" between black box builds and importing assembled components largely depends on the player's preferences, the available resources, and the desired scale of production. It seems from the posts you've shared that players have different strategies and preferences, and the best approach for you might be somewhere in the middle. It could be beneficial to develop black box builds for less complex or smaller scale productions, while setting up centralized production and import systems for commonly used, complex components. As you continue to play and explore, you'll find the method that suits your playstyle best.

- 202307131435: [What is a Blackbox?] The "black box" concept in the context of Dyson Sphere Program (DSP) refers to a type of build where raw materials are inputted and the end products are produced within the same blueprint, without the need for external components. Black box builds have been valued for their self-containment, ease of duplication, and minimizing logistical complexities. However, the drawbacks include potential inefficiencies in resource use and potential issues with scalability. Also, the size of these black box builds can be enormous.