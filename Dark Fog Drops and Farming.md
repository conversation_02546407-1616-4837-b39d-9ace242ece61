---
date: 202407210731
areas: DysonSphereProgram
title: 
description: 
updated: 
---
# Relationships
- :  is a [[Problem]] where Dark Fog Units are to be destroyed and their dropped items are collected for further use. (202412110611)

# Notes

## Blueprints
- 20240211 - Lifestrider - [Flying Spaghetti Monster - Polar Fog Fort w Loot Sorter](https://www.dysonsphereblueprints.com/blueprints/factory-flying-spaghetti-monster-polar-fog-fort-w-loot-sorter)

## Reddit
- 202503100759 - [# Dark fog farming, how to?](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1j7wxr2/dark_fog_farming_how_to/)
- 202502010815 - [# Dark Fog Buildings](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1if62de/dark_fog_buildings/) - Some random question on if chemical plants will get a fogger based tier 3.
- 202411162024 - Gameplay - [# (near)maxed-out dark fog farm](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1gt2c3k/nearmaxedout_dark_fog_farm/) - Single stream, 7 bases aggroed? 6000 units per minute
- 202412091117 - [Dark fog drops](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1haddub/dark_fog_drops/)
- 202412111640 - [Aggro dark fog](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1hc4vit/aggro_dark_fog/)
- 202412022221 - [First Dark Fog Farm](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1h5dzj2/first_dark_fog_farm/)
- 202406142017 - [Dark fog late game farm question](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1dg5c6w/dark_fog_late_game_farm_question/)
- 202406170516 - [Dark fog xp: what is the formula?](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1dhtu8k/dark_fog_xp_what_is_the_formula/)
- 202312280614 - [How do you manage Dark Fog drops?](https://www.reddit.com/r/Dyson_Sphere_Program/comments/18srkt7/how_do_you_manage_dark_fog_drops/)

## Dark Fog Collector Building Phases
- Phase One: Main Layout Area
- Phase Two: Item Creating Item Slots in Stations AND Distributors

## Rule Out
- (1) **Dark Fog Buildings**: Energy Shard, DF Matrix, Recombinator, Silicon Neuron, N Singularity (entry 202504170612)
- (2) **Branches and Logs** (entry 202504170607)
- (3) **Hydrogen, Deuterium, and Fuel Rods** (entry 202504060819)
- (4) **Graphene** (entry 202504170609)

# Log

- 202504170613: [Why is Switching Items Off Important?] This is mostly from a UPS standpoint. The game uses performance to "drop" the item and it is left there for some time. Until it is picked up. I think that's it. Depending on the number of kills, bases, this should add to help UPS

- 202504170612: Dark Fog Specific Items: These are made to either to research or make buildings. Once the research is done and there is a rather large buffer of foggers items and/or buildings made, this can be temporarily switched off

- 202504170609: Graphene thanks to the foggers, is "free" at the start of the game, albeit at a low rate (aren't they all) but when getting to an ice giant, now the graphene is even "more" free. So there's no particular reason to keep this on

- 202504170607: Branches and logs. These are originally made in the first planet to make organic crystals. Either some "rogue" planets can and will be tapped for organic crystals. The amount of branches needed are quite high, and many planets don't even make branches and logs. The factories needed are very small, and the amounts of plants is off ratio from logs I think? After hydrogen, these can be taken out the drop table next. (updated: 202505010613)

- 202504060819: [Turned off Hydrogen from Dark Fog the Moment Hydrogen is Free] Once hydrogen can be tapped from gas giants, it becomes a liability because byproduct collection is done from distributors, but dark fog hydrogen is a sourced distributor. It's easy to turn it off as a drop and the part can be deleted from farms. The same goes for Hydrogen fuel rods because this fuel rod is outclassed by other fuels early on.

- 202504040706: [Items to be Farmed on the Second Planet] On the second planet, there are "some" items that can certainly be left out. These are grouped by (1) Dark Fog Buildings and (2) Already Tapped for Free. (3) not even used at all (4) easily buffered Many of the other items are still debatable (like logs...seriously?) but it will take more time and iteration to confidently rule them out.

- 202504040705: [Items to be Farmed on First Planet] At the first planet, all items are collected. All of the items have some use in the very early game.

- 202504020853: On setting up the pieces for a second fogger planet. Removing a few items that i known for sure are not needed. Having second thoughts of how much fog farming can scale to.

- 202411081138: [Titanium Rates] I think the low rates of titanium and everything else that uses its a just a big ole troll from the developers for anyone whenever tries such a run...like me.

- 202407211039: I should mention here that the raw videos saved in scrapbook take up to 80 GB! That's 1 percent of my entire hard disk space, and 1 percent of my main disk space! If this footage is not used it can quickly turn into an liability...
- 202412110615: [Battle Field Analysis Base Automatically Pick up Dark Fog Drops] When a Dark Fog Unit is destroyed, it has a chance of dropping an item(s) to the ground that lasts for several seconds. They can be manually picked up by Mech, 

- 202412110614: [The Two Kinds of Farm Sorter] There are essentially two types of loot farms depending on the [Battle Field Analysis Base] is configured. The first kind, called [Jammable] because I don't know a better name. Has items collected by the BAB come strait out into a belt unsorted. The unsorted items are carried off in a sushi belt to be sorted elsewhere. The second kind, called [UnJammable] again I can't come up with a better name right now, the BABs are specifically configured to collect only a certain number of items. What's more, those items are specifcally filtered in a specific stream through each of its sorter ports.

- 202412110624: [Why UnJammable Works the Way it Does] A [Battle Field Analysis Base] is configured to pick specific items, so when those very slots are filled, it will not pick up anymore of that loot item, thus the item will decay away. Even more, the specific item is ejected from the BAB using a also specified sorter in a single stream (belt or otherwise) to a single storage box. Even if the box is full, that simply goes back to the slot filled condition on BAB still allowing it to pick up all the other items.

- 202412110641: [Players Usually Farm Using the Jammable Sorter] Most players have went with the Jammable Sorter. It's the easier version to design, and in my opinion, most players have not noticed that an UnJammable version is possible. To discover that will take alot of playing around which is not feasible in a easier setting (not worth it to do) OR a harder setting (no room for experimenting)

- 202412110642: [I Prefer and Have a UnJammable Sorter] Taking inspiration from some other UnJammable setups that have been published in the Dyson Sphere Blueprints site, I have developed a working UnJammable sorter system of 4 modules. Again, it cannot be "overfilled" and stop working.

- 202411090753: [Previous Work] Now I did say I invented an unjammable Dark Fog sorter working title is somewhere along the lines of I completely invented an unclogable Dark Fox sorter but [That's Not Entirely Accurate] thorough search through DSP blueprints will show that hey unjammable DF sorters do exist but to me the real problem is that they're using a ton of belts and last time I checked I didn't see a YouTube video on this soo why not. The only new

- 202411081140: [Previous Work] Now those of you who were nice enough to click on my video thinking I've developed an entirely new concept....well...that may not be entirely accurate. I have seen some early concepts of this idea in blueprints website. 

- 202411090801: [What to Do With Dark Fog] Now you can go ahead and kill every annoying hive in your way...if you even can. And that's fine. However, DF includes table of 60 drops, with usefulness ranging from questionable crap like ??? to completely OP like antimatter. Some drops are absolutely needed for top tier buildings but that's only big deal in end game.

- 202411090733: [I Currently Have a Megabase Vision of All DF Farming] Each of the items have their own drop rates but since it increases with more and more VU and bases, it's not totally out of the question to have our bases completely subsist on DF grinding. Turn DSP Program to DF Program!