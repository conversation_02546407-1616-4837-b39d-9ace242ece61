---
date: 202405140708
areas: DysonSphereProgram
title: 
description: 
updated: 
---
# Relationships
- ************:  are community forums discussing topics on the game [[DSP - 00|Dyson Sphere Program]]

# Ideas
- ************: [Screenshots to Save the Posts] - I just want to know what posts I have clicked on already...so the easiest thing for me to do is to take 10 posts, click on them. Take screenshots of them...and then have [ChatGPT] read them and make out the links for my list. I then hide the post from my sight (I may even make a separate account for that "hidden" list). I'll come up with something else to hide what I have seen already. But clipping these posts are very helpful for making my own repository.

# Prompt
- Reddit Link: I like to follow the DSP Reddit community [here](https://www.reddit.com/r/Dyson_Sphere_Program/). I go to the website from time to time. What I like you to do as just read the post title, link to said title, and the time the title is made. Make a link at the top of the ## Reddit list in the following format: "- YYYYMMDDHHmm - [Discussion Title](Discussion Link)".
- Reddit Comments: Reformat the content below into the concise style. Keep only: • “r/[name of subreddit]” at the top • The post title • The OP’s name and text • The upvote count for the OP • “Comments [number of comments]” • Each commenter’s name, comment text, and upvote count Remove all other lines (e.g., “Downvote,” “Share,” etc.).
- Discord Channels: 
# Notes
- Reddit Forum: https://www.reddit.com/r/Dyson_Sphere_Program/
- DSP Reddit Folder: 202412040736 DSP Reddit
- Screenshots Folder: file:///C:%5CUsers%5Chewcj%5COneDrive%5CPictures%5CScreenshots
- GPT Prompt Reddit Link from Screenshot: Read the screenshot(s) and make a bullet list of entries with the following format in chronological (latest date first) order: YYYYMMDDHHmm - [reddit flair here] - [reddit post title here] (exact web address as read from the browser here) 


## Other Notes
- 202412040734: [About using ChatGPT to Read the Screenshots] Sometimes the bot doesn't read the links properly for example it read (https://reddit.com/r/Dyson_Sphere_Program/comments/1h5dj22/first_dark_fog_farm/) instead of (https://www.reddit.com/r/Dyson_Sphere_Program/comments/1h5dzj2/first_dark_fog_farm/)

**202411261242** - [Modded] - [Distribute Space Warpers mod](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1h0r0n/distribute_space_warpers_mod/)


## Reddit
- https://www.reddit.com/r/Dyson_Sphere_Program/s/UOXx9iYEKK
- 202412022202 - [Help/Question] - [First Dark Fog Farm](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1h5dzj2/first_dark_fog_farm/)
- 202412021543 - [Help/Question] - [Late game Dark Fog energy shards?](https://reddit.com/r/Dyson_Sphere_Program/comments/1h55882/late_game_dark_fog_energy_shards/)
- 202412021128 - [Help/Question] - [Production vs Consumption](https://reddit.com/r/Dyson_Sphere_Program/comments/1h4yxni/production_vs_consumption/)
- 202412021114 - [Help/Question] - [Despawn Dark fog base?](https://reddit.com/r/Dyson_Sphere_Program/comments/1h42svd/despawn_dark_fog_base/)
- 202412020906 - [Help/Question] - [Some basic layouts](https://reddit.com/r/Dyson_Sphere_Program/comments/1h4vpql/some_basic_layouts/)
- 202412020825 - [Screenshots] - [Finally I made it........](https://reddit.com/r/Dyson_Sphere_Program/comments/1h4uw96/finally_i_made_it/)
- 202412012150 - [Screenshots] - [So good to be starting fresh again. :)](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1h4ky1o/so_good_to_be_starting_fresh_again_poor_lil_sone/)
- 202412010929 - [Gameplay] - [New Blueprint Update Tech](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1h44ftk/new_blueprint_update_tech/)
- 202412011843 - [Blueprints] - [My most recent planet project: The Big Science! Produces 14,400 Universe Matrices per minute and consumes my whole CPU. Blueprint link in comments](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1h4xg0b/my_most_recent_planet_project_the_big_science/)
- 202412010647 - [Screenshots] - [Wanted to try Galactic Scale after new update...](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1h41lnv/wanted_to_try_galactic_scale_after_new_update/)
- 202412011032 - [Help/Question] - [Who else has tried max difficulty Dark fog?](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1h4rly5/who_else_has_tried_max_difficulty_dark_fog/)
- 202412010929 - [Gameplay] - [New Blueprint Update Tech](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1h44ftk/new_blueprint_update_tech/)
- 202411291805 - [Help/Question] - [Signal Towers vs Space fog Hives?](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1h2u4yn/signal_towers_vs_space_fog_hives/)
- 202411291721 - [# Xray overload 9.2k/min](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1h2xh8b/xray_overload_92kmin/)
- 202411291508 - [# Signal Towers vs Space Fog Hives?](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1h2ukyn/signal_towers_vs_space_fog_hives/)
- 202411291114 - [Memes] - [Satisfactory VS DSP early game](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1h34z20/satisfactory_vs_dsp_early_game/)
- 202411290708 - [Help/Question] - [Any tips for building:](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1h2ke9/any_tips_for_building/)
- 202411281534 - [Memes] - [Get out of my Star systems!](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1h2rti3/get_out_of_my_star_systems/)
- 202411281522 - [Help/Question] - [Worthwhile](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1h42nc0/worthwhile/)

- 202411301813 - [Memes] - [Hold the splitter and press tab bro. Best decision of my life](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1h3p3hb/hold_the_splitter_and_press_tab_bro_best_decision/)
- 202411280835 - Memes - [When you take too long and the hive does a welfare check.](https://reddit.com/r/Dyson_Sphere_Program/comments/1h1w302/when_you_take_to_long_and_the_hive_does_a_welfare/)
- 202411271902 - Screenshots - [Particle container build finished!](https://reddit.com/r/Dyson_Sphere_Program/comments/1h1iukd/particle_container_build_finished/)
- 202411270629 - [Screenshots] - [Finally starting to build my Dyson Sphere today!!](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1h1273e/finally_starting_to_build_my_dyson_sphere_today/)
- 202411300752 - [News] - [[November 30th] Dyson Sphere Program Patch Notes 0.10.31.24632](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1h3bycc/november_30th_dyson_sphere_program_patch_notes/)
- 202411281558 - [Help/Question] - [Bendy belts?](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1h27y3w/bendy_belts/)
- 202411281534 - [Memes] - [Get out of my Star Systems!](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1h27i3y/get_out_of_my_star_systems/)
- 202411281044 - Screenshots - [How to make precise orbits?](https://reddit.com/r/Dyson_Sphere_Program/comments/1h2cxh5/how_to_make_precise_orbits/)
- 202411270702 - [Screenshots] - [Particle container build finished!](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1hiukd/particle_container_build_finished/)
- 202411291907 - [Off-topic] - [bubble wrap](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1h321h1/bubble_wrap/)
- 202411251443 - [Screenshots] - [Little too close for comfort...](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1h3q0r/little_too_close_for_comfort/)
- 202411280835 - [Memes] - [When you take too long and the hive does a welfare check.](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1hw302/when_you_take_to_long_and_the_hive_does_a_welfare/)
- 202412010647 - [Screenshots] - [Wanted to try Galactic Scale after new update...](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1h11nv/wanted_to_try_galactic_scale_after_new_update/)
- 202412010701 - [Help/Question] - [Power statistics question](https://reddit.com/r/Dyson_Sphere_Program/comments/1h1to4/power_statistics_question/)
- 202411291721 - [Screenshots] - [Xray overload 9.2k/min](https://reddit.com/r/Dyson_Sphere_Program/comments/1xh8b/xray_overload_92kmin/)
- 202412010929 - [Gameplay] - [New Blueprint Update tech](https://reddit.com/r/Dyson_Sphere_Program/comments/1h4ftk/new_blueprint_update_tech/)
- 202411281512 - [Help/Question] - [Worthwhile](https://reddit.com/r/Dyson_Sphere_Program/comments/1h24nco/worthwhile/)
- 202411261242 - [Modded] - [Distribute Space Warpers mod](https://reddit.com/r/Dyson_Sphere_Program/comments/1hr0n/distribute_space_warpers_mod/)
- 202411250606 - [Screenshots] - [star cowboy icarus](https://reddit.com/r/Dyson_Sphere_Program/comments/1gzenl/star_cowboy_icarus/)
- 202411300221 - [Screenshots] - [Shiny stuff in the sky](https://reddit.com/r/Dyson_Sphere_Program/comments/1h3a7x/shiny_stuff_in_the_sky/)
- 202411251036 - [Suggestions/Feedback] - [40 Hours in Suggestions and Thoughts](https://reddit.com/r/Dyson_Sphere_Program/comments/1gzlxn/40_hours_in_suggestions_and_thoughts/)
- 202411261505 - [Screenshots] - [How rare is this? Not the horizontal, but the ore part](https://reddit.com/r/Dyson_Sphere_Program/comments/1h8gq1/how_rare_is_this_not_the_horizontal_but_the_ore/)
- 202411261209 - [Gameplay] - [What's the current state of CPU usage for DS structure points vs absorbed solar sails?](https://reddit.com/r/Dyson_Sphere_Program/comments/1h46s7/whats_the_current_state_of_cpu_usage_for_ds/)
- 202411261937 - [Help/Question] - [Can't see circles of other battlefield analysis bases when placing?](https://reddit.com/r/Dyson_Sphere_Program/comments/1lrl31/cant_see_circles_of_other_battlefield_analysis/)
- 202411251426 - [Help/Question] - [Dark Fog Farm Question](https://reddit.com/r/Dyson_Sphere_Program/comments/1gzue9/dark_fog_farm_question/)
- 202411250807 - [Help/Question] - [Help with complex-planning (factoriolab)](https://reddit.com/r/Dyson_Sphere_Program/comments/1gzienb/help_with_complexplanning_factoriolab/)
- 202411250530 - [Help/Question] - [DSP-Wiki](https://reddit.com/r/Dyson_Sphere_Program/comments/1gzfw6/dspwiki/)
- 202411242144 - [Suggestions/Feedback] - [Off World Oil Refining](https://reddit.com/r/Dyson_Sphere_Program/comments/1gzr6b/off_world_oil_refining/)
- 202411301530 - [Help/Question] - [New game and blue prints](https://reddit.com/r/Dyson_Sphere_Program/comments/1h3lm2u/new_game_and_blue_prints/)
- 202411251207 - [Help/Question] - [Interstellar shipping](https://reddit.com/r/Dyson_Sphere_Program/comments/1gznum8/interstellar_shipping/)
- 202411281044 - [Help/Question] - [How to make precise orbits?](https://reddit.com/r/Dyson_Sphere_Program/comments/1h2xh5/how_to_make_precise_orbits/)
- 202411301504 - [Help/Question] - [How can I get a nice clean organized start?](https://reddit.com/r/Dyson_Sphere_Program/comments/1h3l186/how_can_i_get_a_nice_clean_organized_start/)
- 202411290708 - [Help/Question] - [Any tips for building?](https://reddit.com/r/Dyson_Sphere_Program/comments/1h2ke9s/any_tips_for_building/)
- 202411291141 - [Memes] - [Satisfactory VS DSP Early game](https://reddit.com/r/Dyson_Sphere_Program/comments/1h34r20/satisfactory_vs_dsp_early_game/)
- 202411291508 - [Help/Question] - [Signal Towers vs Space Fog hives](https://reddit.com/r/Dyson_Sphere_Program/comments/1h2ukyn/signal_towers_vs_space_fog_hives/)
- 202411270015 - [Help/Question] - [How to make fine adjustments to logistic filters (slider too small)?](https://reddit.com/r/Dyson_Sphere_Program/comments/1h0wups/how_to_make_fine_adjustments_to_logistic_filters/)
- 202412010827 - [Help/Question] - [Help with framerate control](https://reddit.com/r/Dyson_Sphere_Program/comments/1h39m3/help_with_framerate_control/)
- 202412010340 - [Help/Question] - [Planner](https://reddit.com/r/Dyson_Sphere_Program/comments/1h3z0vk/planner/)
- 202411261848 - [Help/Question] - [I stopped acquiring metadata after adding some mods a while ago. The game never told me anything. Am I screwed?](https://reddit.com/r/Dyson_Sphere_Program/comments/1h0t0fr/i_stopped_acquiring_metadata_after_adding_some/)
- 202411261133 - [Help/Question] - [Stars dimming?](https://reddit.com/r/Dyson_Sphere_Program/comments/1h0g1ws/stars_dimming/)
- 202411301208 - [Help/Question] - [Need help onm a supply !](https://reddit.com/r/Dyson_Sphere_Program/comments/1h3h55z/need_help_onm_a_supply/)
- 202411300302 - [Help/Question] - [Start system](https://reddit.com/r/Dyson_Sphere_Program/comments/1h37v42/start_system/)


# Relationships
-  is a reddit forum where people discuss on the game Dyson Sphere Program

# Objective
- Create a easy to read, easy to graph, history of the posts made in the  forum
- Document reading through the reddit.

# Issues





# Links
- https://www.reddit.com/r/Dyson_Sphere_Program/
- Scrapbook: [20240607 Reddit - Dyson Sphere Program](file:///D:%5CScrapbook%5C20240607%20Reddit%20-%20Dyson%20Sphere%20Program)



# Log
202406110654: First 24 Hours
202406080738: I'm not intending to -ever- work with the reddit API. They are way to restrictive. Instead I will probably work with html scrapers




# Hydrogen

## Looping hydrogen for deuterium
- https://www.reddit.com/r/Dyson_Sphere_Program/comments/1c6ofu6/looping_hydrogen_for_deuterium/

### Jan (Mistral Instruct 7B Q4)
The poster is discussing their setup in the game "Dyson Sphere Program" where they have a logistics station that collects hydrogen from orbital collectors on a gas giant, and then runs it through fractionators to produce deuterium. They have a loop of hydrogen going around the pole of their planet, with fractionators along the way that feed deuterium into storage tanks. The excess hydrogen is burned for power in thermal generators and used to make nuclear fuel rods. The poster is unsure if they can eventually turn all hydrogen into deuterium with enough passes through the fractionators or if the game keeps track of hydrogen that has already been processed.

The community response is confirming that it's possible to convert all hydrogen into deuterium with enough passes through the fractionators, but also mentioning that power will eventually become a limiting factor and suggesting various strategies for managing the hydrogen and deuterium production. Some posters are sharing their own experiences and offering suggestions on how to optimize the setup. Overall, it seems that the community is supportive and helpful in answering the poster's question and providing additional information and tips for managing resources in Dyson Sphere Program.

### ChatGPT (GPT-4)
The text discusses a gaming strategy where a player has set up an efficient system to convert hydrogen into deuterium in a game, likely "Dyson Sphere Program". The player collects hydrogen from gas giants using orbital collectors and then processes it through fractionators in a looping belt system at the pole of a planet. The fractionators convert the hydrogen into deuterium, which is then stored and used for various purposes, including burning excess hydrogen for power and making nuclear fuel rods. This setup has led to a surplus of deuterium and fuel rods, suggesting a potentially unlimited supply of these resources if managed properly. The player is considering expanding this setup to sustain a loop of fusion reactors and potentially power additional operations like mining on other planets. There's a discussion on the effectiveness of this system and whether the game keeps track of hydrogen that has been processed multiple times through fractionators. Other gamers share their insights and strategies, emphasizing the game’s scale and the continual need for resources even when they seem abundant.

