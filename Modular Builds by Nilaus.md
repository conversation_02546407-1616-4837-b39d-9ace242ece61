---
areas: DysonSphereProgram
date: '202211280810'
title: 
description: 
updated: 
---
```vid
https://www.youtube.com/watch?v=9Q2CBjhtKJ0
Title: 3 Essential Builds for Early, Mid & Late Game | Dyson Sphere Program | Tutorial / Master Class
Author: <PERSON><PERSON>
Thumbnail: Attachments/YouTubeThumbnails/9Q2CBjhtKJ0.jpg
AuthorUrl: https://www.youtube.com/@<PERSON>laus
```

- Video presentation by [<PERSON><PERSON>]
- Goes through some [Planet Templating] before addressing the builds
- Generated Key Points (by Claude2):
	- I will bring you another class today on the three very intro bills that will be constraining your base growth in early to the game. These bills are the basics that you absolutely need.
	- I sent you the ring, particle containers, and electromagnetic coils - also known as green engines, blue engines, and pink containers.
	- I have been putting some thought into this of course to make the best possible designs and I hope you appreciate it.
	- As always, these designs are available in the link description to my website. That is where there's a link to all my sites designs.
	- The designs are automatically available on my Google Drive layers.
	- The designs are of course available to patrons. I really appreciate all people who are supporting.
	- Let's take a look at the three builds I want to present that need to be built in the early game and late game.
	- I made a final "max level" design of what you see here. I considered making an early game version, but there are too many variables like maybe you only have mark 1 belts or mark 2 assemblers.
	- So I've decided to present the final design and you can downgrade it to your level. When you upgrade, it's easy to update the blueprint.
	- This is a consistent design - it takes up x squares here, y there, etc. The placement allows fitting these together efficiently.
	- The first build makes electromagnetic coils. It's the basic ingredients - iron and copper. This is the first ratio.
	- The idea is a consistent blueprint you build up over time, not relying on bringing in external parts from other factories.
	- I'm including a monitor to alert if no items move for 60 seconds, indicating a supply issue. I do this for all my builds.
	- The second build makes particle containers in a 10x10 squares area. You likely only need 2-4, not more.
	- There's a specific way to place it to maximize space as I show with the markers. Follow the video to place it precisely.
	- This also uses max level buildings. Bring in items if needed until you unlock the logistics components I use.
	- The third build makes planetary logistics. Place it precisely as shown to fit together with the others.
	- With 4-12 of these 3 builds, you should be set for a long time. They don't take up much room but provide what you need.
	- The builds are available in my shared library. Let me know if you find any mistakes to fix. I'll update the Google Drive with new versions.
	- I hope this overview was useful for setting up your early and late game. Stay effective!