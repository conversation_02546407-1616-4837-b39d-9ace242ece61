---
date: 202501240710
areas: DysonSphereProgram
title: 
description: 
updated: 
---
# Relationships
-  is a [[Game Updates]]

# Ideas
- 202502191049: [Have the Devs Seen Station Info?] There is a mod for the game called "Station Info" it was toggled planetary brief view of each of the stations (including advanced miners). This is more useful than a dashboard doing the same thing. In fact, the devs closest version of this solution is to mouse over a station just to see what the contents were, as if just clicking on it wouldn't do that same thing. 

- 202502191048: [Dashboards as a Roadmap] What I found my own dashboards useful for was as a roadmap for my megabase that can take many weeks to design and build. It was nice getting a overall picture of where I was at, and what was left to do.
# Script
## Introduction
OH my god...this is bad...

this is soo bad...

Once again keeping up with the foggers is using up more ammo than I can make

And the limited titanium is limiting all the research progress

I don't know what to...

Wait a second, what's that icon there? Never seen that before...

Whooooa...where the f--- am I?

Oh I see.

It's an update.

Hello engineers and welcome back to Dyson Sphere Program

Stapledon would be so proud

I feel like my last update video was just yesterday

and what do you know...YouthCat sends off another update.

Now it is a bit smaller compared to the November update,

at first glance it's not much 

but there are a few interesting quality of life changes to mention

and the interesting introduction of the "dashboard"

what is this dashboard about then?

Well let's find out.

## Railgun Retargeting
First let's get the railgun retargeting out of the way. 

So yeah, Rail guns are these buildings here that deploy solar sails in whatever orbit you have designed and have it set to shoot at

Before, the railguns were a bit limited, they only shoot as long as the targeted orbit is in it's line of sight.

So if the gun is on the night side you're out of luck

Also in some edge cases where the gun the can you, shoot at another orbit...well you ARE in luck

because now you can set railguns to target at whatever orbit you have planned. If you want it to.

So basically if you just want a sphere going as quickly as possible just set two orbits crossing each other like this

and set the guns to target either orbit, that SHOULD get the guns working as much as possible, probably

Goodbye Railgun retargeting mod, and thank you brokenmass for your service

Wait does this mean I need to sound off every mod that's been killed by an update?

(goodbye bottleneck...and thank you .... for your service)
## Blueprint Foundation
Clip: 2025-02-08 15-13-15 t1:30

Blueprint mode gets a very very very requested feature.

Automatically adding foundation.

So when you can't lay down a blueprint because of "Foundation required"

Somewhere in that wall of text you are told to press space bar 

and walla pesky foundation gone

I'm sure putting foundation on entire planets will still be a thing

But this QoL is a great time saver and we can finally preserve the natural beuty of some planets out there

Or not
## Screenshot Mode
Next is Screenshot Mode

The main feature here is the ability to take a very high resolution screen shot of your planets, 

(Wait, can I take pictures of the universe view and blueprint as well?)

And there is some camera controls added as well

I'm going to admit the camera controls are kinda cumbersome to use

But that's fine. Totally worth it to get that unforgettable 8k shot

and as far as I know, we now have a way in vanilla to pause the game while having a limited view of some stuff around the planet

That's pretty much it for QoL, there was tease about a sign post system

custom game goals maybe?

anticipating it's coming at a later date
## Tesla Tower and Wind Turbine Click Drag
As a bonus feature, the developers did a small patch where they add a small feature

Basically, the tesla tower and wind turbine can be click dragged, just like factories

Acutally i was pretty sure turbines had that feature already

WHhat about wireless towers and satellite substation?

And do they have to snap to grid?
## Intro to Dashboard System
OK title card features done...main event...the dashboard system

As you've probably guessed it's a screen where you move in your favorite statistics

So as your have probably noticed, I have played Dyson Sphere for quite a bit.

So naturally, I have seen some mods. Some of them quite good even.

In particular stats mods that I guessing some of you are not familiar with

LSTM, Planet Info, Station Info...Oh god Station Info. Better Stats that later became Bottleneck, that later became deprecated from the Game Goals update.

Sorry I'm digressing...what I am saying is I have seen some good useful presentations

and putting it out there now...I would say about 80 percent of the dashboard is repeating what I can find elsewhere

and yes, I think the developers are already aware of this, and as of this video, there was a minor patch in February 8 where indeed the dashboard and its widgets were "optimized"

Hopefully I can offer some constructive critism on each of the widgets added or who knows maybe there is a cool technique that hasn't been found yet.

Again...I'm not here to tell you what to do, I'm here to tell you what could happen

let's go through all the widgets I have seen so far
### Dashboard Widgets (Stats)
- Production
- Power
- Storage
- Logistics
- (Planet) Resource
- Research
- Dyson Sphere
- Planetary Shield
- Vein
- Facility Production
- CPU Performance (They forgot this one?)
## Production Stats
Of the 200 or so items all of their stats are sorted by location 

And surprising, they can be sorted further by 1-min, 10-min, and 60-min sample durations.

In other words, there's ALOT of possible widgets to be seeing in the dashboard.

Perhaps you can add the ones critical to infrastructure like fuel and spray, but otherwise expect to only be adding in a handful of items of interest.
## Power
Next we have power.

Most of the time I'm only concerted about one thing absolute capacity and absolute demand

I see the damand it goes up. I increase capacity with more generators. Make more fuel if needed. Done

In dashboard the 1x1 shows the capacity next to demand. 

The Icon itself is a working radial chart.

The 2x1 is the same thing, basically.

The 3x1 adds in accumulator stats and planetary shield consumption.

Is that total energy consumption in the widget, really?

And finally the 3x3 size throws in the assets and their power demand/generation.

So yeah, solid 2 on this one. I would just add the 1x1 for each planet.

Unless you have a few dozen planets to look at...

Can we get something "like" a 1x1 but in a list for all the planets?

kthx
## Storage (Boxes)
In early game this widget can kinda of be helpful since there is probably not that many anyway...

I don't know you can for example set a widget for your titanium and silicon ore on your second planet. 

They get full, go pick it up.

Or vice versa.

Whatever.
## Logistics

From what I there are no widgets for distributors

## (Planet) Resource
In planet view our dashboard button can be found in the planet resources table

The planetary and interstellar logistics widgets is nothing special, just shows the item amounts in transit.

I think there is a 30 item limit because I'm pretty sure there is more being moved around

anyway the resource stats adds the amount of resources, and ocean types, just as you would see in the planet view

BUT...it adds just a tiny bit more

it seems to include all of the ore patches in the planet represented as a line

and each line is a dot appears to represent a single vein

I'm not entirely sure what the brightness means, it could mean it has been tapped, running out, I'm not sure.

Because it seems the number of viens on a planet is exclusive to Dashboard,

but hey you pile all your favorite midgame mining planets in here

## Research
Adding in a research widget puts up 

Oh my gosh that is very useful to know put THAT in my front screen
## Dyson Sphere

I would rate this a 1
## Planetary Shield
for Planetary Shield Stats the 1x1-Brief is just the shield hp

The 1x1-Detailed also includes the fill ratio/coverage

The 2x1 is...quite almost the same info as 1x1-Detailed, but perhaps you like to see shield hp with more precision

The 2x3 and 3x2 sizes includes the coverage map

I just want to tell if the foggers were somehow shooting at my planet and it's about to die

Yeah! Just like that!
## Veins
All of the widget sizes except for the 2x2 pretty much just show the total amount of ore in the patch and the number of veins in the patch

The 2x2 is interesting, all the individual veins are displayed as a bunch of circles

Now if only they can just open up all of these for a planet...
## Facility Production (Producer?)
This one seems interesting as it's a widget for a single factory.

The 1x1 just shows the item being made and the expected production rate. The radial chart showing the cycle progress is not shown but that's probably a bug

The 2x1 does show the cycle progress, along with power demand, and spray status

3x1 changes the cycle circle to a cycle bar? and include input and output items in it's buffer

Just like item stats only expect to use this for some really, really important factories

Here I've made a battery of ray receiver widgets. Kinda fun to observe actually

Did you know that I once recommended to have the cycle progress shown in EVERY visible factory in planet view?

That would be cool, but I digress...again....
## Traffic Monitor
This one seems to suffer the most from the lack of labeling. 

Because of that, I can't even put in more than one without getting confused.

And I can just use the alarm notification anyway

Solid 2 I thought this was a very cool widget to have

now I'm not so sure.
## Conveyor Belt
Now this one interesting

The contents of a belt segement belt contents can be shown in a widget. Not particularly cool in a belt with a single item

but this widget can maybe serve the same purpose of a traffic monitor widget

What's really cool is the widget seems to track all the items in the belt, even if there are multiple items in it

Very useful, I intend to include this in my early game sushi belts to see how well they are working
## Dashboard Issues
In my opinion the biggest setback that I see about the dashboard is that I am locked from the rest of the game

Even when production stats were opened I could still move around

In dashboard I can see stuff behind me still running and I'm not able to do anything

But actually this can be very exciting because it can easily be flipped to one of the biggest features...since...factory games!

Imagine...any of these custom widgets can go ANYWHERE in the screen

and if I dare speak...include pages that can be cycled manually or automatically???

Compared to what I just mentioned other issues seem kinda minor so I'm not even going to mention them. Though I would like to mention better working titles or annotations for the widgets?

So yeah, good game feature that I feel needs just one more good swing to hammer it down.

## That GameJam Announcement

"GameJam" where they and I quote "Explore some innovative ideas and improve our abilities". Feature development part of the game is paused. While bugfixes and optimizations are worked on as usual. See you in two months? What am I going to do till then?









# Notes
- Folder: 202501241704 DSP Update 20250123 (Dashboard Update)


## Discussions
- 20250126 - [# Dyson Sphere Program / Фундамент чертежей, Автоорбита, Дэшборд / Патч 0.10.32.25496](https://www.youtube.com/watch?v=Vnvko08cNsg) - 








- [Comment for the Devs] The devs are doing a great job extend a heartfelt thanks for experience this amazing game.

- [Dashboard] I CAN'T PUT THE DASHBOARD IN THE FUCKING SCREEN I CAN'T THE PUT THE DASHBOARD IN THE SCREEN WHAT DIFFERENCE DOES THIS MAKE COMPARED TO THE NORMAL PANEL WHY





- 



## Log
- 202501231740 - [This is going to be how we show off our stats from now on...](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1i8g8wu/this_is_going_to_be_how_we_show_off_our_stats/)
- 202501231107 - [2025 Jan 23rd Dyson Sphere Program Patch Notes V0.10.32.25496](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1i86t3w/2025_jan_23rd_dyson_sphere_program_patch_notes/)



