---
date: "202307121044"
areas: DysonSphereProgram
title: 
description: 
updated: 
---
# Relationships
-  is a type of [[Buildings]].

# Notes
## YouTube
- 20250129 - [Overhead belt factory overhaul - First Playthrough - EP 25](https://www.youtube.com/watch?v=BhGJedByia0) - A seemingly normal DSP YouTube, until I see this guy actually tilting belts 90 degrees, and somehow the sorters are pointing straight DOWN to their factory ports. Effectively making some sort of sorter elevator!
## DSP Blueprints

## Reddit
- 202502091639 - [TIL you can tilt conveyers with the left and right arrow keys...](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1ilokr0/) - A little discovery the belts can tilt but not quite sure of what else can be done. Do they know about the sorters...


## Data
Sorter Rates (rates are transfers per minute, this is important because of cargo stacking)
	T1-3 = 120
	T1-2 = 180
	T1-1 = 360
	T2-3 = 240
	T2-2 = 360
	T2-1 = 720
	T3-3 = 480 
	T3-2 = 720
	T3-1 = 1440

## Discussions
- 202503032310 - [Is anyone actually weird enough to optimize belt/sorter levels with throughput?](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1j32bc0/is_anyone_actually_weird_enough_to_optimize/)


sorter only mixers for sushi belts > there are many combinations, normal and EX versions, and are saved in blueprint book

# Log
- 202504020937: [Do Sorters with Filter Take More UPS] In theory, a sorter with a filter will have have lower UPS (more calculation) involved, because after the game determines there is an item that "could" be moved, it must then proceed to check if the item passes through the sorter. Compared to the overall performance cost of the sorter, there are usually bigger issues at play, such as the total number of sorters, but should be beared in mind. (in other words, avoid uneeded filters on sorters.)

- 202502082144: [A Moving Sorter is Better than a Still Sorter] There was this player who finished the game nd gave his stats. Best sorter performance I have seen. He admits to intentionally using slower and upgrading as needed. I saw some theories that code performs.better when the sorter is in constant movement. Perhaps when when it is not moving the game is checking every tick for a slide transfer which uses more simulation resources.