---
date: 202503050927
areas: DysonSphereProgram
title: 
description: 
updated: 
---
# Relationships

# Output
## Technology and Upgrade Tree Pathway (Comments)
- 202503132117 DSP Technology Tree
- 
# Ideas
- 202503120922: [This Could Make an Interesting Website] I always thought of how advanced players using metadata can plan out a newgame+ with some technologies and upgrades already unlocked and what it cost in total. Or to plan out a tech tree route without having to go shopping every time the research que is empty. This could make for a website where problems around the technology and upgrade tree can be taken care of.

- 202503091556: [Research IDs] Around 4 years ago tehlazyAsian shows the ID's for console (in-game developer commands) [Reddit](https://www.reddit.com/r/Dyson_Sphere_Program/comments/lxvm9v/tech_ids_for_console/). 

- 202503070556: [There is Much Lore to Unlock in Tech Tree] I'm sure they didn't just put some of these technologies in there without research. Someone researched about what it could take to make a Dyson Sphere and included each of them with a small story of how it works, in a small book taking the form of a tech tree. Yes, there is more to it than just a game view. In time perhaps, I'll plan to "expand" on those technology descriptions for a more bigger book...


# Prompt
## Finish List
Below is a list of technologies and upgrades for the Factory game Dyson Sphere. Some the technologies include the parenthesis and abridged "cost" of said technology or research in parenthesis. The cost is suffix with the most advanced matrix (blue - Electromagnetic matrix, red - energy matrix, yellow.- structure matrix, purple - information matrix, green - gravity matrix, white - universe matrix. Complete the rest of the list based on assumed rules and format of the read list, especially the costs of each technology and upgrade. 
# Notes

```vid
https://youtu.be/Bym9CU5UbJA
Title: DYSON SPHERE PROGRAM ►10 NOT Blindingly Obvious Tips  ► New Factory Simulation Strategy Game 2021
Author: Skye Storme
Thumbnail: https://i.ytimg.com/vi/Bym9CU5UbJA/mqdefault.jpg
AuthorUrl: https://www.youtube.com/@SkyeStorme
```


## To Do
- Entire Tech Tree (make graphic of entire tree at expansion lv2 and "lv4")
	- May need "free" photo editor for legal reasons



## Main Notes

- The technologies and upgrades are mostly divided by cubes, but then I try to further divide them to other phases like pre-cube, post-cube, stations, etc
- A brief description of what the unlock and/or upgrade does is included.
- There is also what I call a "typical time" of the technology, based on some posted runs I have seen in YouTube (links at the end)
- Checkboxes are included so everything can be checked off step-by-step
- Some techs here are not actually mandatory because the runs are "short" runs that mostly end at mission completion. Some techs are completely left out. In another version, I'll think about what a 100% tree would look like
- Many of these runs were done without combat. Fortunately, a few combat runs (even max difficulty) are starting to get posted. Links also at the end
- I also assume you are not "speed running" just for the mission complete, intended to 100% the technologies, and maybe expand a sphere or two, so no achievements
- The only non-combat uses I see in the "combat" techs is BAB and maybe signal tower. Otherwise everything else can be skipped in no combat???
- I wrote this from a 100% perspective, where once you are able to make the cube, get all the tech that needs that cube first before proceeding with the next cubes
- Some distances are expressed in degrees, as a rule-of-thumb, 10º is about 45m for a standard planet.
- I added in the costs of the researches but their "most expensive cube" and suffixed with a letter of the cube (b - blue, r - red, y - yellow, p - purple, g - green, w - white). Many of the technologies cost that cube and an equal amount of the cubes before it)
- To show the costs I just leave the highest costed cube with the letter at the end for each of it. Most of the time that works fine but for some really lopsided costs I put down cost for each cube
- Infinites are white cube upgrades that go really, really high (I stopped at 500 :D ) I leave equations for the level and a few notable levels and effect
- Some of the white cube research grow quadratically (the growing cost is linear) while appling a linear benefit (ie. diminishing return). Veins Utilization is the only upgrades that applies an exponential benefit ( the opposite, a non-exponential cost per level more while given a exponential growing benefit (ie. explosion return)
- On some of the white cubes, I skip a couple of levels, and the cost I put down is -only- for that level, so there are more white cubes spent in between the levels. It's mostly to show how high those upgrades go to and the benefit they ultimately give.
- Communication is different in that at level 88 the construction drones are capped at 256 but the levels can continue to go up. In practice only around 50 drones can be launched at a time from one spot.


- [x] Electromagnetism (10coils) (turbine, tesla tower, miner)
- [x] Automatic Metallurgy (10 coils, 10 boards) (smelter, glass)
- [x] Basic Logistics System (10 boards, 10 gears) (mk1/yellow belt, mk1 sorter, mk1 box)
- [x] Basic Assembling (10 coils, 10 gears) (assembling machine mk1)
- [x] <span style="color: blue;">Electromagnetic Matrix (10 coil, 10 board) (b - blue cube, matrix lab)</span>
- [x] Mecha Core lv1 (20 iron, 20 copper) (durability 1800) (laser range 11m) (energy 200MJ)
- [x] Universe Exploration lv1 (10b) (planet resource view)
- [x] Weapon System (20b) (combat, gauss turret, magnum ammo box)
- [x] Combustible Unit (120b) (combustible unit, mech fuel, combat grenades)
- [x] Smelting Purification (100b) (silicon ore, high purity silicon, energetic graphite)
- [x] Semiconductor Material (200b) (microcrystalline silicon)
- [x] Engine (20b)
- [x] Battlefield Analysis Base (100b) (bab, combat repair, additional construction drones)
- [x] Energy Circuit (60 log, 60 coal, 60 graphite) (fuel chamber power 0.8+0.2 MW)
- [x] Auto Reconstruction Marking 1 (200b) (marking rate 1/s)
- [x] Drive Engine 1 (150 coal, 50 engine) (mecha flight 2.5x speed)
- [x] Missile Turret (150b) (missile turret, missile set)
- [x] Mass Construction 1 (100 board) (150 facility blueprints)
- [x] Communication Control lv1 (100b) (Construction Drones 1x4) 
- [x] Drone Engine 1 (200b) (Construction Drone Flight Speed 8 m/s)
- [x] Steel Smelting (120b) (steel, wind turbines in water)
- [x] Reclamation (400b) (foundation)
- [x] Thermal Power (30b) (thermal power station, to d-rods, to green cube)
- [x] Electromagnetic Drive (50b) (electromagnetic motor)
- [x] Inventory Capacity 1 (120 steel, 120 board) (50 Slots)
- [x] Mechanical Frame 1 (60 electric motor) (movement 7+1 m/s) 
- [x] Upgraded Logistics System 2 (100b) (splitter, sorter mk2, traffic monitor)
- [x] Energy Shield 1 (100b) (radius 2m, resistance 50kJ/hp, capacity 100 MJ)
- [x] Kinetic Weapon Damage 1 (100b) (110%)
- [x] Energy Weapon Damage 1 (100b) (110%)
- [x] Explosive Weapon Damage 1 (100b) (110%)
- [x] Enhanced Structure (200b) (durability coefficient 1.04)
- [x] Mass Construction 2 (300b) (300 facility blueprints)
- [x] Fluid Storage Encapsulation (50b) (storage tank, water pump)
- [x] High Efficiency Plasma Control (50b) (prism, plasma exciter, wireless power tower)
- [x] Proliferator 1 (200b) (proliferator mk1, spray coater)
- [x] Plasma Extract Refining (100b) (oil extractor, oil refiner)
- [x] Prototype (200b) (ground squad 1x4)
- [x] Processor (800b)
- [x] Basic Chemical Engineering (200b) (chemical plant, plastic, sulfuric acid)
- [x] Solar Collection (200b) (solar panel)
- [x] Applied Superconductor (400b) (graphene)
- [x] Universe Exploration 2 (200b) (system resource view)
- [x] <span style="color: red;">Energy Matrix (200b) (r - red cube)</span>
- [x] Mecha Core 2 (100r) (energy 200+200 MJ, durability 1872+300, laser damage 26+2hp, laser range 11+1m) (note: it looks like enhanced structure bonus is post-applied to mech)
- [x] Veins Utilization 1 (400r) (speed 110%, loss 94%, debris 104%)
- [x] Energy Circuit 2 (400r) (power 1.0+0.2MW)
- [x] Crystal Smelting (500r) (diamond, crystalline silicon)
- [x] Signal Tower (400r)
- [x] Magnetic Levitation (400b, 100r) (electromagnet turbine aka green motor)
- [x] Implosion Cannon (300b 300r) (implosion cannon, shell set)
- [x] Magnetic Particle Trap (1600b, 800r) (particle container)
- [x] Planetary Defense System (600r) (laser turret, planetary shield generator)
- [x] Proliferator 2 (800b, 600r)
- [x] Communication Control 2 (200r) (construction drone 1x6)
- [x] Communication Control 3 (400r) (1+1 task points)
- [x] Drone Engine 2 (400r) (construction drone 10m/s)
- [x] Mass Construction 3 (500r) (900 facilities blueprints)
- [x] Distribution Logistics System (600b, 300r) (distributors, logistics bots)
- [x] Distribution Range (400r) (bot coverage 60º)
- [x] Mechanical Frame 2 (200r) (mech speed 8-20m/s)
- [x] Inventory Capacity 2 (200r) (50+10 slots)
- [x] Mechanical Frame 3 (400r) (mech speed 9-22.5m/s)
- [x] Energy Shield 2 (100r) (radius 2.0+0.5m, resistance 50.0kJ/hp, capacity 100+200 MJ)
- [x] Kinetic Weapon Damage 1 (200r) (120%)
- [x] Energy Weapon Damage 1 (200r) (120%)
- [x] Explosive Weapon Damage 1 (200r) (120%)
- [x] Enhanced Structure 2 (400b) (durability coefficient 1.08)
- [x] Drive Engine 2 (200r) (cruise mode in SPACE)
- [x] Titanium Smelting (200r) (titanium ingot)
- [x] Photon Frequency Conversion (200r) (photon combiner)
- [x] Super Magnetic Field Generator (1000b, 250r) (super magnetic ring aka blue motor, unlock slope limit...belt slope)
- [x] High Efficiency Logistics System 3 (400b, 100r) (mk2/green belt, mk3/blue sorter, mk2 box)
- [x] Hydrogen Fuel Rod (400r)
- [x] Thruster (1000r)
- [x] Vertical Construction 1 (400r) (belt 15, depot/splitter 3, lab 5)
- [x] Planetary Logistics System (800b, 400r) (mk3/blue belt, PLS, drones)
- [x] Solar Sail Orbit System (300r) (solar sails, EM-rail ejector)
- [x] Ray Receiver (600r)
- [x] Geothermal Extraction (800b, 800r) (geothermal power station)
- [x] X-Ray Cracking (400r)
- [x] Reformed Refinement (500r)
- [x] Deuterium Fractionation (200b, 300r) (fractionator)
- [x] Polymer Chemical Engineering (400r) (organic crystal)
- [x] High Strength Crystal (600r) (titanium crystal)
- [x] High Speed Assembling (600b, 300r) (mk2 assembler, replicator speed 150%)
- [x] Precision Drone (400r)
- [x] Combat Drone Damage 1 (300r) (110%)
- [x] Combat Drone Attack Speed 1 (200r) (speed 110%)
- [x] Combat Drone Durability 1 (200r) (110%)
- [x] Ground Squadron Expansion 1 (200r) (ground squad 1x6)
- [x] Jammer Tower (400b, 800r) (jammer tower, jamming capsule)
- [x] EM Weapon Strength 1 (100r) (105%)
- [x] EM Weapon Strength 2 (200r) (110%)
- [x] Auto Reconstruction Marking 2 (400r) (marking rate 3/s)
- [x] Vertical Construction 2 (600r) (belt 21, depot/splitter 4, lab 7)
- [x] Energy Storage (600r) (accumulator)
- [x] Reinforced Thruster (1600r)
- [x] Explosive Unit (400b, 200r)
- [x] High Explosive Shell Set (400r)
- [x] Titanium Ammo Box (600b, 600r)
- [x] <span style="color: yellow;">Structure Matrix (800b, 800r) (y - yellow cube)</span>
- [x] Mecha Core 3 (300y) (energy 400+400 MJ, hp 1500+852+300, l.damage 28+2, l.range 12+2m)
- [x] Vein Utilization 2 (1000y) (mining speed 120%, ore use 88%, debris 108%)
- [x] Energy Circuit 3 (1200y) (power 1.2+0.2MW)
- [x] High Strength Titanium Alloy (800b, 800r, 80y) (titanium alloy)
- [x] Interstellar Logistics System (ILS, Vessel)
- [x] High Strength Material (600r, 150y) (carbon nanotube)
- [x] Mini Fusion Power Generation (1000b, 500r, 250y) (fusion power plant, d-rod)
- [x] Proliferator 3 (800b, 600r, 400y)
- [x] Distribution Range 2 (600y) (range 80º)
- [x] Distribution Range 3 (800y) (range 110º)
- [x] Interstellar Power Transmission (1200b, 1200r, 120y) (Energy Exchanger)
- [x] Gas Giants Exploitation (1200y) (Orbital Collector)

- [x] Logistics Carrier Engine 1 (400y) (drone speed 9.6m/s, bot speed 6.6m/s)
- [x] Logistics Carrier Engine 2 (600y) (drone speed 11.2/s, bot speed 7.2m/s)
- [x] Logistics Carrier Capacity 1 (400y) (bot 9 items, drone 30 items)
- [x] Logistics Carrier Capacity 2 (600y) (bot 10 items, drone 35 items)
- [x] Logistics Carrier Engine 3 (800y) (bot 8.1m/s, drone 13.6m/s, vessel 900m/s)
- [x] Logistics Carrier Capacity 3 (800y) (bot 12 items, drone 40 items, vessel 300 items)
- [x] Logistics Carrier Capacity 4 (1000y) (bot 14 items, drone 50 items, vessel 400 items)

- [x] Energy Shield 3 (200y) (radius 2.5+0.5m, capacity 300+200MJ, shield burst)
- [x] Kinetic Weapon Damage 3 (400y) (120%)
- [x] Energy Weapon Damage 3 (400y) (120%)
- [x] Explosive Weapon Damage 3 (400y) (120%)
- [x] Enhanced Structure 3 (600y) (durability 1.12) 
- [x] EM Weapon Strength 3 (400y) (120%)
- [x] Planetary Shield 1 (200y) (181 kJ/hp)
- [x] Planetary Shield 2 (400y)

- [x] Attack Drone (500y)
- [x] Combat Drone Damage 2 (500y) (fighter dmg 120%)
- [x] Combat Drone Attack Speed 2 (400y) (120%)
- [x] Combat Drone Durability 2 (400y) (fighter hp 120%)
- [x] Ground Squadron Expansion 2 (600y) (squad 1x8)
- [x] Planetary Shield 2 (400y) (160 kJ/hp)

- [x] Mechanical Frame 4 (600y) (movement 10-25m/s)
- [x] Inventory Capacity 3 (400y) (70 slots, 4x logistics stack)
- [x] Inventory Capacity 4 (600y) (80 slots, 2 logistics columns, 6x logistics stack)
- [x] Inventory Capacity 5 (800y) (100 slots, logistics stacks 8x)
- [x] Mechanical Frame 5 (800y) (movement 11-27.5m/s)
- [x] Drive Engine 3 (1000y) (cruise speed 2000m/s)

- [x] Communication Control 4 (600y) (6+3 construction drones)
- [x] Drone Engine 3 (800y) (construction speed 10+2m/s)
- [x] Mass Construction 4 (800y) (3600 facility blueprints)

- [x] High Strength Glass (800r, 400y) (titanium glass)
- [x] Casimir Crystal (800b, 800r, 800y)
- [x] Miniature Particle Collider (800b, 800r, 800y)
- [x] Strange Matter (1000b, 1000r, 1000y)
- [x] Gravitational Wave Refraction (1200y) (graviton lens, warper)
- [x] High-Strength Lightweight Structure (1200b, 200y) (frame material, Dyson sphere component)
- [x] Particle Control (800b, 800r, 200y) (particle broadband)

- [x] Supersonic Missile Set (800b, 800r, 200y)
- [x] Suppressing Capsule (600y)
- [x] Superalloy Ammo Box (600b, 600r, 300y)
- [x] Satellite Power Distribution System (800y) (satellite substation)

- [x] Solar Sail Life 1 (400y) (5400+300s)
- [x] Solar Sail Life 2 (600y) (5700+300s)
- [x] Vertical Construction 3 (800r, 800y) (belt 27, depot/splitter 5, lab 9)
- [x] Vertical Construction 4 (1000y) (belt 33, depot/splitter 6, lab 11)
- [x] Solar Sail Life 3 (800y) (6000+600s)
- [x] Vertical Construction 5 (1600y) (belt 39, depot/splitter 7, lab 13)
- [x] Auto Reconstruction Marking 3 (800y) (marking 6/s)

- [x] Ray Transmission Efficiency 1 (600y) (dissipation 63%)
- [x] Ray Transmission Efficiency 2 (800y) (dissipation 56.7%)
- [x] Ray Transmission Efficiency 3 (1000y) (dissipation 51%)

- [x] Integrated Logistics System (1000b, 500r, 50y) (automatic piler, pile sorter)
- [x] Pile Sorter Upgrade 1 (500y) (load/input 2-stack cargos)
- [x] Pile Sorter Upgrade 2 (800y) (load/input 3-stack cargos)
- [x] Pile Sorter Upgrade 3 (1000y) (feed/output 3-stack cargos)
- [x] Pile Sorter Upgrade 4 (1200y) (load 4-stack cargos)
- [x] Pile Sorter Upgrade 5 (1500y) (feed 4-stack cargos)

- [x] <span style="color: purple;">Information Matrix (800b, 800r) (p - purple cube)</span>
- [x] Vein Utilization 3 (1000p) (speed 130%, ore loss 83%, debris 112%)
- [x] Vein Utilization 4 (1600p) (speed 140%, ore loss 78%, debris 116%)

- [x] Mecha Core lv4 (500p) (energy 800+800MJ, hp 3200, laser dmg 30+2hp, laser range 14+2m)
- [x] Energy Circuit 4 (2000y) (1.4+0.2MW)
- [x] Drive Engine 4 (2000y) (unlock warp speed 0.2 ly/s)
- [x] Mechanical Frame 6 (1000p) (mech speed 12-30m/s)
- [x] Mechanical Frame 7 (1200p) (mech speed 14-35m/s)
- [x] Universe Exploration 3 (1000p) (6-ly system view)

- [x] Distribution Range 4 (1000p) (coverage 140º)
- [x] Communication Control lv5 (1000p) (construction drones 2x (9+3) )
- [x] Drone Engine lv4 (1200p) (construction drone speed 12+3m/s)
- [x] Mass Construction lv5 (1000p) (unlimited facility blueprint)
- [x] Logistic Carrier Engine 4 (1000p)
- [x] Logistics Carrier Capacity 6 (1600p) (bot 16+2, drone 60+10, vessel 500+100)

- [x] Energy Shield 4 (600p) (radius 4m, capacity 700 MJ)
- [x] Kinetic Weapon Damage 4 (600p) (170%)
- [x] Energy Weapon Damage 4 (800p) (170%)
- [x] Explosive Weapon Damage 4 (600p) (170%)
- [x] Enhanced Structure lv4 (durability 1.16)
- [x] EM Weapon Strength 4 (600p) (120+10%)
- [x] Planetary Shield 3 (600p) (137 kJ/hp)

- [x] Crystal Explosive Unit (600b, 600r, 600y, 300p)
- [x] Crystal Shell Set (800p)
- [x] Plasma Turret (1000b, 1000r, 1000y , 500p)

- [x] Corvette (600p) (fleet 1x4)
- [x] Combat Drone Damage 3 (800p) (squad dmg 120+15%, fleet dmg 125+25%)
- [x] Combat Drone Attack Speed 3 (800p) (squad attack speed 120+20%, fleet attack speed (100+30%)
- [x] Combat Drone Durability 3 (600p) (squad hp 120+20%, fleet hp 100+30%)
- [x] Ground Squadron Expansion 3 (1600p) (squad 2x8)
- [x] Space Fleet Expansion 1 (400p) (fleet 1x8)
- [x] Space Fleet Expansion 2 (800p) (fleet 1x12)
- [x] Ground Squadron Expansion 4 (2400p) (squad 2x12)

- [x] Auto Reconstruction Marking 4 (1200p) (15/s)
- [x] Ray Transmission Efficiency 4 (1200p) (46%)
- [x] Ray Transmission Efficiency lv5 (1400p) (41%)
- [x] Research Speed (1000p)
- [x] Research Speed 2 (2000p) (+60 hash/s) (300%)
- [x] Space Fleet Expansion 3 (1800p) (fleet 2x12)

- [x] Wave Function Interference (1200b, 1200r, 200p) (plane filter)
- [x] Quantum Chip (800b, 800r, 800p)
- [x] Plane-Filter Smelting (1000p) (plane smelter)
- [x] Mesoscopic Quantum Entanglement (1000p) (quantum chemical plant)
- [x] Quantum Printing (800p) (mk3 assembler, replicator speed 200%)

- [x] Vertical Launching Silo (1600p) (vls, small carrier rocket)
- [x] Solar Sail Life 4 (1000p) (6600+600s)
- [x] Solar Sail Life 5 (1600p) (7200+900s)
- [x] Solar Sail Life 6 (2000p) (8100+900s)
- [x] Research Speed 3 (4000p)

- [x] <span style="color: green;">Gravity Matrix (1600b, 1600r, 1600y) (g - green cube)</span>

- [x] Vein Utilization 5 (2000g) (speed 150%, loss 73.39%, debris 120%)
- [x] Mecha Core 5 (800g) (energy 1.6+1.6GJ, 1500+1740+300hp, laser range 16+2m, laser dmg 32+2)
- [x] Inventory Capacity 6 (1000g) (120 slots, logistics stack 10x)
- [x] Energy Circuit 5 (3000g) (power 2.4 MW)
- [x] Photon Spotlight Mining (1000bryp, 600g) (adv miner)
- [x] Mechanical Frame 8 (2000g) (mech speed 16-40m/s)
- [x] Universe Exploration 4 (2000g) (view all resources)
- [x] Drive Engine 5 (3000g) (warp speed 0.25ly/s)

- [x] Distribution Range lv5 (1200g) (180º)
- [x] Communication Control 6 (1600g) (construction drones (2+2)x12)
- [x] Logistics Carrier Engine 5 (1200g) (bot 9.9m/s, drone 19.2m/s, vessel 1500m/s 0.13 ly/s)
- [x] Logistics Carrier Engine 6 (1600-g) (bot 10.7/s, drone 22.4m/s, 1800m/s 0.15 ly/s)
- [x] Logistics Carrier Capacity 7 (2000g) (bot 20, drone 80, vessel 800)

- [x] Gravity Missile Set (1000g)

- [x] Energy Shield 5 (1000g) (radius 5m, capacity 1GJ)
- [x] Kinetic Weapon Damage 5 (1000g) (140+30%)
- [x] Energy Weapon Damage 5 (2000g) (140+30%)
- [x] Explosive Weapon Damage 5 (1000g) (140+30%) 
- [x] Enhanced Structure 5 (1500g) (durability 1.20)
- [x] Planetary Shield 4 (1000g) (117 kJ/hp)
- [x] EM Weapon Strength 5 (1000g) (EM 140%)

- [x] Destroyer (800g)
- [x] Combat Drone Damage 4 (1000g) (squad and fleet dmg 150%)
- [x] Combat Drone Attack Speed lv4 (1000g) (squad and fleet speed 160%)
- [x] Combat Drone Durability lv4 (1000g) (squad 160%, fleet 160%)
- [x] Ground Squadron Expansion 5 (4000g) (squad 4x12) 
- [x] Space Fleet Expansion 4 (4000g) (fleet 3x12)
- [x] Space Fleet Expansion 5 (6000g) (fleet 4x12)

- [x] Vertical Construction 6 (2000g) (belt 49, depot/splitter 8/lab 15)
- [x] Drone Engine 5 (1600g) (construction drone speed 18m/s)
- [x] Planetary Ionosphere Utilization (2000g) (lens powered ray receivers)
- [x] Dirac Inversion Mechanism (3000/3000/750/750/1500) (antimatter from ray receivers)
- [x] Controlled Annihilation Reaction (4000r, 2000g) (constraint sphere, anti-matter fuel rod
- [x] Artificial Star (1200g)

- [x] Pile Sorter Upgrade (2000g) (load and unload SIMULTANOUSLY)
- [x] Logistics Carrier Capacity 8 (4000g) (drone 100, vessel 1000)
- [x] Auto Reconstruction Marking 5 (1600g) (marking 30/s)

- [x] Solar Sail Attaching Speed lv1 (600g) (15/min)
- [x] Solar Sail Attaching Speed lv2 (1000g) (30/min)
- [x] Ray Transmission Efficiency 6 (1600g) (dissipation 37.2%)
- [x] Ray Transmission Efficiency 7 (1800g) (dissipation 31.6%)

- [x] Dyson Sphere Stress System 1 (2000g) (0+15º)
- [x] Solar Sail Attaching Speed lv3 (2500g) (60/min)
- [x] Dyson Sphere Stress System 2 (3000g) (30º)
- [x] Dyson Sphere Stress System 3 (4000g) (45º)
- [x] Solar Sail Attaching Speed 4 (4000g) (120/min)
- [x] Dyson Sphere Stress System 4 (5000g) (60º)
- [x] Dyson Sphere Stress System 5 (6000g) (75º)
- [x] Dyson Sphere Stress System 6 (7000g) (90º)
- [x] Solar Sail Attaching Speed 5 (8000g) (240/min)
- [x] Solar Sail Attaching Speed 6 (12000g) (360/min)

- [x] <span style="color: white; filter: brightness(1.5);">Universe Matrix (2000g) (w - white cube)</span>
- [x] Mission Completed! (4000w) (get encouraging message)
- [x] Vein Utilization 6 (4000w) (1.6X Speed, 68.99% vein, 124% debris)

- [ ] Inventory Capacity 7 (1500w) (slots 12x12, logistics 3x12x10)
- [ ] Logistics Carrier Capacity 9 (6000w) (drone 120, vessel 1200, PLS 6000, ILS 12000)
- [ ] Station Integrated Logistics 1 (8000w) (2-stack cargo)
- [ ] Logistics Carrier Capacity 10 (8000w) (drone 140, vessel 1400, PLS 7000, ILS 14000)
- [ ] Logistics Carrier Capacity 11 (10000w) (drone 160, vessel 1600, PLS 8000, ILS 16000)
- [ ] Logistics Carrier Capacity 12 (12000w) (drone 180, vessel 1800, PLS 9000, ILS 18000)
- [ ] Logistics Carrier Capacity 13 (16000w) (drone 200, vessel 2000, PLS 10000, ILS 20000)
- [ ] Station Integrated Logistics 2 (16000w) (3-stack cargo)
- [ ] Station Integrated Logistics 3 (24000w) (4-stack cargo)
- [ ] Drive Engine 6 (6000w) (warp 0.30 ly/s)
- [ ] Drive Engine 7 (14000w) (warp 0.35 ly/s)
- [ ] Drive Engine 8 (24000w) (warp 0.40 ly/s)
- [ ] Drive Engine 9 (36000w) (warp 0.45 ly/s)
- [ ] Drive Engine 10 (50000w) (warp 0.50 ly/s)
- [ ] Drive Engine 20 (300kw) (warp 1.00 ly/s)
- [ ] Drive Engine 30 (750kw) (warp 1.50 ly/s)
- [ ] Drive Engine 40 (1.4Mw) (warp 2.00 ly/s)
- [ ] Drive Engine 50 (2.25Mw) (warp 2.50 ly/s)
- [ ] Drive Engine 60 (???Mw) (warp 3.00 ly/s)
- [ ] Drive Engine 70 (4.55Mw) (warp 3.50 ly/s)
- [ ] Drive Engine 80 (6.00Mw) (warp 4.00 ly/s)
- [ ] Drive Engine 90 (7.65Mkw) (warp 4.5 ly/s)
- [ ] Drive Engine 100 (9.50Mw) (warp 5.0 ly/s)
- [ ] Drone Engine 6 (2000w) (construction drone speed 21m/s)
- [ ] Drone Engine 7 (8000w) (construction drone speed 24m/s)
- [ ] Drone Engine 8 (18000w) (construction drone speed 27m/s)
- [ ] Drone Engine 9 (32000w) (construction drone speed 30m/s)
- [ ] Drone Engine 10 (50000w) (construction drone speed 33m/s)
- [ ] Drone Engine 11 (72000w) (construction drone speed 36m/s)
- [ ] Drone Engine 12 (98000w) (construction drone speed 39m/s)
- [ ] Drone Engine 15 (200kw) (construction drone speed 48m/s)
- [ ] Drone Engine 20 (450kw) (construction drone speed 60m/s)
- [ ] Drone Engine 24 (722kw) (construction drone speed 75m/s)
- [ ] Combat Drone Attack Speed 5 (1600w) (squad and fleet 200%)
- [ ] Combat Drone Durability 5 2000w (squad and fleet 200%)
- [ ] Planetary Shield 5 (2000w) (100 kJ/hp)
- [ ] Auto Reconstruction Marking (3200w) (auto-marking uncapped?)
- [ ] EM Weapon Strength 6 (4000w) (150%)
- [ ] Ground Squad Expansion 6 (6000w) (squad 6x12)
- [ ] Ground Squadron Expansion (8000w) (squad 8x12)
- [ ] Space Fleet Expansion (8000w) (fleet 6x12)
- [ ] Space Fleet Expansion (12000w) (fleet 8x12)
- [ ] Energy Shield 6 (2000w) (radius  6m, capacity 1.5GJ)
- [ ] Energy Shield 7 (6000w) (radius 7m, capacity 2.0GJ)
- [ ] Energy Shield 8 (12000w) (radius 8m, capacity 2.5GJ)
- [ ] Energy Shield 9 (20000w) (radius 9m, capacity 3.0GJ)
- [ ] Energy Shield 10 (30000w) (radius 10m, capacity 3.5GJ)
- [ ] Energy Shield 15 (110kw) (radius 15m, capacity 6.0GJ)
- [ ] Energy Shield 20 (240kw) (radius 20m, capacity 8.5GJ)
- [ ] Energy Shield 25 (420kw) (radius 25m, capacity 11.0GJ)



## WHITE INFINITES
- Veins Utilization (ore loss -6%, mining speed +10%, debris +4% per lv)
- Logistics Carrier Engine (drone speed +50%, bot speed +20%, vessel speed +50% per lv)
- Enhanced Structure (durability +4% per lv)
- Combat Drone Damage (squad and fleet +20% per lv)
- Kinetic Weapon Damage (+20% per lv)
- Energy Weapon Damage (+20% per lv)
- Explosive Weapon Damage (+20% per lv)
- Research Speed (+60 Hash/s / +100% per lv)
- Ray Transmission Efficiency (dissipation -15% per lv)
- Energy Shield (capacity 0.5GJ per lv)
- Energy Circuit (power +1.00MW per lv)
- Communication Control (construction drones +3 per lv)
- Mecha Core (energy +2.40GJ, hp +200, dmg +2.0 per lv)

