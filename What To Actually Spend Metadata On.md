---
areas: DysonSphereProgram
date: '202401050540'
title: 
description: 
updated: 
---
https://www.youtube.com/watch?v=E8AEpEKAuDk


- Greetings engineers, I come to you from a new cluster where I
- There a bunch of techs that I wish I just had right now? But oh wait...I don't have to wish, I can just 

- 
- Well that's the entire concept of what is colloqially known as NewGame+, and here in Dyson Sphere Program, that concept is implemented as Metadata.
- First quick rundown of how metadata works
- You have a save...to get metadata, your save cubes of a certain color for one hour
- It is also tagged with the author's name that is checked against your own so no you cannot grab other people's saves and put them on your own
- While at infinite resources and max combat difficulty there is a 840% metadata output multiplier
- And at scarce resources and max comat diffcityul gives the highest multiplier at 3000% multiplier.
- Current available ammount. This is what you allowed to spend (mmhhmmm i mean instantiate) in your current save, at this current pointi n time
- Current game contribution is how much the save you are in right now is "contributing"
- And of course we are all save scummers so we keep multiple saves of the same cluster...the game checks which of those are making the most metadata and counts that one.
- And then there is current game and total instantiation where it is talling the total metadata you have spent hmmmmt instnatiated.
- And yes, this system has a no refund policy...even deleting all the saves that you spent metadata on does not give it back.
-  But if you do feel you have been wronged and obviously youthcat does not a metadata customer support then you can perhaps reserach on a possible way to get your metadata refunded. You've been warned.
- As of the combat update so the metadata multiplier is also tied with combat difficulty and... resource multiplier difficultty yeah.
- Note that even in infinite resources and no combat, you still get some metadata, albeit at a penalty of 40%.


- while I'm at it let's take a look at dressing up. Instantiate cubes and...


- There will be basically two tiers of spending, you either a little of metadata or alot of metadata.
- If you have a little metadata...just go through the game. I assure you everyway you slice it that is by far the fastest way to get your techs. 
- For those with alot well...you are either being cheap like I am or you don't see the point in getting it.
- And that I suppose is the probablem with the metadata...I suppose the 
- FIrst...let's suppose you have very little metadata to spend...how about enough for one skill...what would it be?
- That said I wouldn't spend this on the upgrades instead of the technologies because the technologies has a TON of perquisites which can add up quite quickly and it is basically more efficient to just them the old fashioned way
- Though I will mention High Speed Assembling and Quantum Printing which gives +50 replicator each which is kinda cool to have early? I'm not probably not buying it but might try it sometime
- Also there are the earilier in the game. Again...probably not worth it because of all the prerequireste techs to be spending on.
- Mechanical Frame: 
- In my current save I made the mistake of fighting two fronts. After personally taking out one wave in one side I find myself rushing over to the other side for the next wave...which of course made me thought of what it would like to speed myself to where I need to go. This combined with drive engine I and your mech will go where ever it is needed very fast.
- Inventory Capacity + Distribution Logistics System: 
- I still remember how annoying it is to run out of space and it's so painful to either find a box for my stuff or outright delete them
- Even more so with this with the dark fog just giving away free crap to you. Well maxing this out early takes care of ALOT of that problem. Also logistics slots are also included in the upgrade. 
- Did you know you can replicate every building in the game?
- So one idea is to just have all the main components carted to you by bot and while basic buildings are strongly reocmmended to be automaticed, you basically don't have to worry about malls or hubs for the entire tutorial.
- Drive Engine II / Drive Engine III: Don't like your planet? Want to deviate from the main direction? Have another planet in the cluster in mind? Well sadly you can't just go another planet in your drop ship but you can just grab these skills, a little bit of coal fuel, and be on your way to the planet you actually like in a few hours. Weird.. Get Core III / Engine III to get there in half the time.
- Drone Engine:
- Another annoyance besides being so slow...are your constuction drones being so slow...max these out. Communcation control which increases the number of drones was in there though I should mention that battlefield analysis bases have a set of drones of their own and you can have as many as them as you want
- Mass Constuction:
- Just max this out and you can slam down any blueprint you want
- Energy Shield:
- Vein Utilization
- I think this may net some metadata in the low resource areas?
- Weapon Damage
- Yeah this too good to pass up on. When Maxed turrets and drones can have up to double damage which will throw off the entire balance with dark fog. 
- That's as far as I know. Metadata is good concept and although not overpowering (as it shouldn't) it has











- High guys I want to play a game with you







- How scarce you ask? Well you see this copper ore here??? There like what about half an hour left...and that's just ammo. There is no more ammo to keep this base secure and then its...back to homeland!
- But...enough of my upcoming demise. In this video I'm going to go over the next couple of techs and upgrades that might help me last a little longer kay?
- First let's talk about what to research first. My main goal here is combat viability but some of this stuff.


-
- Remember that if you do spend any metadata there is a no refund policy.
- That said I'd research the following first to improve survivability
- In fact some upgrades are worth every penny...cube...of metadata I'll indicate that...right HERE
- Mecha Core
	- Basically turns your squishy industrial mech to a kick ass GUNDAM. 
	- OK not really...but it's a good improvement and besides we need it to unlock other key upgrades.
- Drone Engine 
	- So those construction drones run out faster...
- Mechanical Frame
	- (improves mech walk/fly speed...for running away from mobs better)
- Those are the 3 upgrades to look out for and in my opinion the best to spend metadata on
- So what's next then...
- Auto Reconstruction Marking...
	- Auto build broken buildings enough said
- 
- I don't want to think about this too much there is too much out there alraedy
- Now let's talk technologies BY FAR first and foremost the #1 to get is
- BAB
	- Battlefield analysis base. Let's face it...our construction drones SUCK in this update compared to this thing. The poor guys are now doing triple the work...constructing buildings, replacing broke buildings, and now repairing buildings mid combat. They also kinda got a bit dumb...rushing out several meters away from your position to repair that windmill?
	- I think there is a setting to adjust their priorities...somewhere...
	-  Anyway this is where there BAB (I'm calling it that for now). LOOK AT THIS it has +12 construction drones of it's own!!! 
	- Can you think about it...remember those communication drones we were going to spend a vast amounts of metadata on? just for what +10 drones? all of that replaced with 1 BAB?
	- and you can have MORE them?
	- AND And of course they can repair AND construct buildings as well. While auto picking up loot from the enemies! AND with some setup automatically make the buildings it's assigned to replace???
	- This is building is looking to be the MVB (most valuable building) this update possibly the entire GAME...with missiles being a noticeable second. But yeah some bodies got be second regardless.
- Missiles
	- Missile Turrets...OMG the missiles turrets. The missiles are very very good all stages of the game me thinks. Splash damage, can shoots at space targets. Full planet coverage with signal towers. Oh yes signal towers
- Signal Tower
	- Signal Towers work with missile turrets. A bit too well acutally...they do cost reds though so if you feel like getting this a bit earlier by my guess spending metadata on this. 
- So in conclusion: 1st is the BAB...2nd is the BAB...3rd is the BAB...
- Then missiles 
- and maybe signal towers depending on how fast you want to get
- Mechanical frame and communication engine are "speed" upgrades that I think should be upgraded asap.
- 
- So yeah...these techs and upgrades should help alot for combat readiness. and OK great...there's more to find. ALOT more but hopefully this is a quick run down of what to go after soon. Especially on harder difficulties.
- Now all of these upgrades feel so far away...well for experienced players there metadata.
- Metadata are currency for building up factories in your previous games. They can then be spent in your other games...as of now you can spend metadata for conveniently unlock techs and upgrades.
- Or put your mech back together when it's destroyed. Personally I'm not seeing that being too useful...compared to just going back to the last save.
- Distribution Range...yeah
	- Getting bots early are the thing...however they come pre-nerfed with a very limited range. Here I don't mind spending the metadata to make them be able to reach anywhere in the planet. Though I wouldn't want to try..they are still a bit slow...
	- Mechanical Frame for the walk speed
	- 
- Hope you found this video useful. Check you later.