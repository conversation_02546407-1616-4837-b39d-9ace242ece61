---
date: 202410272024
areas:
  - DysonSphereProgram
title: 
description: 
updated: "202410272025"
---
# Relationships
- are savable [[Layouts]]



# Notes
- [Falk Blueprints](https://steamcommunity.com/sharedfiles/filedetails/?l=german&id=2373611284) - very early blueprints but relatable concepts
- [Dyson Sphere Blueprints](https://www.dysonsphereblueprints.com/) - website containing shareable blueprints. getting kinda outdated





## Reddit
- 202501120554 - [Super Magnetic Rings Blueprint](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1hzkaeg/super_magnetic_rings_blueprint/) - 93.75m, No quite liking it, but appreciate the share and short discussion on Reddit.


# Log
- 202503021320: Deleted the blueprints google spread sheet. It was intended as a just a checklist and list of my blueprints. Sadly I've found it to only be redundant in front of my active blueprints folder. Checklist and list of my blueprints is easily done and maintained in-game or out-game with windows folder explorer! (though it would be nice to somehow access the notes part of the file...)
- 202410280746: [Found My Missing Blueprints] After some deep searching through EVERYTHING I have, I found my leftover blueprints in my laptop. Need to salvage EVERYTHING that I can!
- 202410272024: Need to find where I put my strange matter blueprint! 
- 202502102137: [Auto Index My Blueprint Files] I like to keep a quick index of all my blueprints in a spread sheet. However, I prefer not to update this list by hand. Instead, I will use some sort of automation to group all the blueprints by have by whatever category, stage level, and size which can help me tell where I am at with all my blueprints.

- 202412281810: [Blueprint Books That Have Been Made Available By Other People] Other players that have played the early game extensively have been generous enough to share their "books" of blueprints usually through google drive or even their personal website.

- 202412281815: [Single Blueprints Have Been Posted in a Website] To my knowledge there is only one website [dysonsphereblueprints](https://www.dysonsphereblueprints.com/) that is dedicated to storing, and even reading (displaying simple info like buildings used, ect) blueprints uploaded by players.


