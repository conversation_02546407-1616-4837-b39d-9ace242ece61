.block-language-vid{
	position: relative;
	/* display: block; */
	--corner-radius: 8px;
}

.block-language-vid~.edit-block-button{
	z-index: 10;
}

.block-language-vid>a.thumbnail{
	display: flex;
	flex-direction: row;
	color: var(--text-default);
	text-decoration: none;
	margin: 10px 0;
	max-width: 400px;
	width: 100%;
	/* border-radius: var(--corner-radius); */
	/* overflow: hidden; */
	box-shadow: none;
}

.block-language-vid .thumbnail-text{
	/* Negative left margin so that the hover color extends under image's rounded corners */
	padding: 0 16px;
	margin-left: -8px;
	flex-grow: 1;
	flex-shrink: 1;
	line-height: 1.3;
	border-radius: 0 var(--corner-radius) var(--corner-radius) 0;
	transition: background 0.3s;
	z-index: 1;
}

/* .block-language-vid>a:hover{
	box-shadow: 0 .2rem .5rem var(--background-modifier-box-shadow);
} */

.block-language-vid>a.thumbnail:hover .thumbnail-text{
	background: var(--background-primary-alt);
}

.block-language-vid .thumbnail-title{
	position: relative;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
}

.block-language-vid .thumbnail-img-container{
	position: relative;
	margin: 0;
	padding: 0;
	width: 150px;
	flex-shrink: 0;
	flex-grow: 0;

	.thumbnail-img{
		display: block;
		object-fit: contain;
		border-radius: var(--corner-radius);
		position: relative;
		z-index: 2;
	}

	.img-icons-container{
		display: flex;
		position: absolute;
		bottom: 4px;
		left: 4px;
		height: 18px;
		width: 100%;
		flex-direction: row;
		align-items: center;
		justify-content: stretch;
		z-index: 3;
		gap: 4px;

		.thumbnail-playlist-svg{
			height: 100%;
			background: rgba(0, 0, 0, 0.6);
			border-radius: 4px;
			padding: 1px;
		}

		.timestamp{
			font-size: calc(var(--font-text-size) - 5px);
			display: block;
			padding: 0 5px 0 5px;
			background: rgba(0, 0, 0, 0.6);
			border-radius: 4px;
		}
	}
}


.block-language-vid .thumbnail-author{
	display: block;
	color: var(--text-faint);
	font-size: calc(var(--font-text-size) - 3px);
	width: fit-content;
	margin-top: 3px;
	text-decoration: none;
	transition: color 0.1s;
}

.block-language-vid .thumbnail-author:hover{
	color: var(--text-muted);
}


.block-language-vid .thumbnail-error{
	color: var(--text-error);
}

.block-language-vid .dummy-container{
	position: relative;
	height: 84.34px;
	width: 400px;
	/* background: var(--background-primary-alt); */
	/* background: linear-gradient(-80deg, var(--background-primary-alt) 45%, rgba(125, 125, 125, 0.2) 50%, var(--background-primary-alt) 55%); */
	background: linear-gradient(-80deg, rgba(125, 125, 125, 0.05) 45%, rgba(125, 125, 125, 0.2) 50%, rgba(125, 125, 125, 0.05) 55%);
	animation: gradient 2s linear infinite;
	background-size: 400% 400%;
	margin: 10px 0;
	opacity: 1;
	/* transition: opacity 1s linear;
	&.hidden{
		position: absolute;
		opacity: 0;
	} */
}

@keyframes gradient {
	0% {
		background-position: 100% 50%;
	}

	60% {
		background-position: 0% 50%;
	}

	100% {
		background-position: 0% 50%;
	}
}

.block-language-vid .dummy-image{
	height: 100%;
	width: 150px;
	background: rgba(125, 125, 125, 0.2);
}

.block-language-vid .dummy-title{
	position: absolute;
	left: 160px;
	top: 6px;
	height: 20px;
	width: calc(100% - 170px);
	background: rgba(125, 125, 125, 0.2);
}

.mod-settings .setting-item.default-attachment-info{
	border: none;
	padding-top: 0;
}

.mod-settings .setting-item.default-attachment-info input{
	color: var(--text-muted);
}

.mod-settings .setting-item.default-attachment-info .setting-item-description{
	font-style: italic;
}

/**** SUPPORT FOR CARD-STYLE THUMBNAILS ****/
.kanban-plugin__item-title .block-language-vid,
:not(.kanban-plugin__item-title) .block-language-vid.thumbnail-card-style{
	&>a.thumbnail{
		/* margin: auto; */
		position: relative;
		flex-direction: column;
		max-width: 190px;
	}
	.thumbnail-img-container{
		width: 100%;
	}
	.thumbnail-text{
		padding: 12px 2px 2px 2px;
		margin: -8px 0px;
		border-radius: 0 0 var(--corner-radius) var(--corner-radius);
	}
}

/* :not(.kanban-plugin__item-title) .block-language-vid.thumbnail-card-style {
	&>a.thumbnail {
		margin: auto;
		flex-direction: column;
		max-width: 250px;
	}

	.thumbnail-img {
		width: 100%;
	}

	.thumbnail-text {
		padding: 16px 2px;
		margin: -8px 0px;
	}
} */
