---
date: ************
areas:
  - DysonSphereProgram
title: 
description: 
updated: "************"
---
# Relationships
- is a [[Buildings]]
- uses [[Interstellar Logistics Vessel]]

# Ideas
- ************: [Power is Not Calcaluated of ILS in FacotorioLab] Even though it accounts for power to run a certain number of factories, it doesn't count power consumed by ILS/PLS/Distributors to transport said items around. For planning all of the routes has to be calculated as it can be a significant portion of the power budget.

- ************: [How Far Does A System Have to Be To Limit An ILS?] A single ILS has a charge rate up to 300 MW with a max charge of 12.0 GJ. If my math is still current, an 80 ly system would consume 6.5 GJ just to send 1 vessel to! The math goes further but thanks to the 300 MW charge limit, this severely limits the throughput of a single ILS and hence multiple ILS would typically be needed. As EdibleOedipus was saying (************) putting in them on the single planet or peppering them in some sort of controlled pony express route (using "Vessel transport range" or that "Priority Settings" thing) works fine. 

# Notes
## Log
- ******** - The developers has updated the logistics station system including a revamp on its priority system. See [DSP Update ******** (Station Update)]. The update seems to be movated behind "feedback" from the players.


## Reddit
- ************ - [# This is the craziest star cluster seed I've ever seen. The black hole is nearly 80 light years away and that little blue star is 85. Has anyone seen a larger galaxy? Seed 0178 5298](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1j57zkk/this_is_the_craziest_star_cluster_seed_ive_ever/) - When talking about seeds, there is talk about how to vessel transport becomes limited at a longer distances between systems.