---
areas: DysonSphereProgram
date: '202401152004'
title: 
description: 
updated: 
---
- Well isn't this humiliating. I spend all that time making a blueprint to make my game easier.
- In my last video I showed an "example" of making a black box blueprint. A Blackbox in this context is a production chain where inputs (usually raw) are converted to outputs by a series of production steps that no one cares about. 
- Eventually I'll do that for everything and win the game, but for the time being I wanted to settle for structure matrix, aka yellow cubes, aka the newb filter, aka what other games can I play
- And while I'm aware there are better designs out there, I have reached a whole new low finding out the hard way that I can't use this oversized monster. Serves me right!
- So this video I'm going to show what I did to set things right this time. Going more for less: going from 60 to 90 yellow cubes per minute while keeping up with the 900 size limit
- I'll do it better...not shooting the for the best here...just better.
- And then just as a sanity check I'll use it in game for you.
- OK here we go.
- So...it's back to the sandbox mode...I just love this part. This part where I am starting at nothing wondering how to start.
- I suppose in theory I can just hand crank yellow cubes or something...buy why...when I can...make something better?
- Those better blueprints that I mentioned? they had some differences but they were hitting up to 180 per minute while keeping at the 900 limit, so I know what I'm trying to do is possible.
- Ok enough staring...I think I should start with what I made previously
- So my goal is to improve the layout from an output of 60 to 90 yellow cubes per minute, and keeping it below the 900 limit so it can be used as early as possible in the game.
- While it's quite similar to what was done before I'll break down the changes into three parts.
- As usual, I'll start at the top of the line with the yellow labs. Around 8 to 10 will suffice here.
- And put a pole in between the two towers. This is actually a dumb habit to put power poles in this early but in the back of my mind I just want those damn unpowered warning lights to go away.
- 
- Of course finding ways to get sorters to work out from the belt is the fun part. It's can really packed with the things sometimes. Notice that I'm doing these layouts at a zone where where the grids are relatively smaller, so I won't get surprised about building colisions. Not as much anyway.
- Ok belts are rearranged for all steps toward the yellow cubes. Each with belts running between pairs of factories. I think I'll add spray production in here. Might as well since green spray is made with diamond and coal which is exactly what is going into this black box.w

- Well times a wasting so let's just skip to the good part. Everything you see here hopefully you are familiar with. There are the expected number of factories needed to make 90 per minute yellows while trying to spray everything for extra product.
- and yes I'm still trying to spray everything. I'm no longer constraining myself to putting all the spray coaters in some line. At least not all the time. This should help in fixing the shape of the layout into a more useable square as opposed to long as heck rectangle.
- and let's put down the smelters that make the diamonds. Oh yes, this is good time to mention all these boxes littered around here. As you know there is dark fog, and in the event I happen to be farming them it will probably be a good idea to place these boxes to serve as a supplemental reserve from dark fog drops. When the box is full it will be pumping the drops into the output belt and since it's last in first out...these items will be prioritized first through the output. That's the idea anyway.
- As it stands I have manually put in the drops in order to use them, but if it works out well, then I'll later add logistics bots so they will automatically fill these boxes from dark fog loot.
- What I want to really want to improve, or rather keep doing, is my belt work. A very good rule do go by is that a double sided belt is more efficient than a single sided belt. A double sided belt is a belt with factories on both sides. So instead of using a long belt to get items into a single row of factories it can be accomplished all the same with a shorter belt with double row of factories.
- Those of you who are not familiar with my style and theory, basically it is usually straightforward to figure out how many factories is needed but what is not straightforward is how to move all the intermediate and products around. That I think makes the difference between a good layout or design and a great layout or design and is something I strive for all the time.
- Am I still not done here with the diamond? Obviously the diamond is meant to turn into yellow cubes but it's also meant for mk2 proliferator so I trying to figure out how I can make that happen.
- well before I figure out a solution, my ADHD sets in and I'm already distracting myself with something else. Oh yeah, did I ever show this tip in my channel? I like to call it blue print cut and paste. The way it works is you copy an area in blueprint mode. then ctrl+v to start to paste the temporary blueprint. Then leave blueprint to delete what was just copied. and then ctrl+v again to paste. The cool part is what you just copied is still the so called clipboard so this is very useful way to nudge large parts of a layout that you working on.
- ok time for a unessarily long connection of the graphite below the diamond above. I feel there is a better way to arrange this but my goal is just to keep everything below the 900 limit.
- And so far I am at 769 so not bad so far.
- Just revisiting the list of factories. I just want to check if there are not areas that need more than what a single belt can handle. Thanks to my overkill decision to use only mk2 belts here that shouldn't be problem here.
- As far as refined oil goes, I'm seeing there are two destinations for it, the plastic and the organic crystals. So I'll have to get that out of the way somehow.
- speaking of being silly I once AGAIN move the diamonds more to the middle of the build...I think it's still the same amount of belts being use but I suppose I just want some semblance of symmetry in this mess.
- and I just want to put in the mk1 and mk2 proliferator in here. Who knew that just getting 4 factories would be so much trouble. Oh right me.
- What did I tell you? I swear I'm like a caveman trying to figure out how to get square pegs into circular holes. The craziesness of trying to spray the mark 1 spray before being made into mark 2 spray is not lost on me.
- and let's nudge those dimong smelts the for 100th time to somewhere more snug.
- that should be spray the mk 1 spray, but what am I spraying the mk2 spray with?
- Here I'm just fidgeting around with this trash hydrogen tank. It's in the way of my spray belt that's supposed to go straight up and I'd be damned if I have to do just one more bend on this blueprint.
- Let's put in the blueprint
- So yeah...quick recap max difficulty, farming three base farm, blue, reds, trying to get yellows automated.
- 
- 
- where are we at then? Yes, my blues and red are developed ok at 60 or so per minute. And on to getting yellow cubes setup...and that pretty much my 14 hours...kinda of.
- Just looking for a good spot to put there...it's from raws that are belted in so yeah as long as that coal, oil, 
- I want to put it right there...there is plenty of unused space, belting the raws in shouldn't be too much trouble....
- I'll just have to evict the steel away...put it closer to the iron patch...over there...
- Is it just me or does does construction speed feel even worse this patch? I think I've been away from early game way way way too long and it feels like I have to put down BAB's just make construction speed feel normal.
- and I'll just trim this old fasioned organic crystals. I never calculated the factory ratios here but it turns out there were too many factories on the job here in the first place.
- A couple of distractions later...like 4 of them. It's time to put down the blueprint. Seriously do I have to put down a BAB everytime I build something this big...
- Only missing ingredient here is the titanium ingots. Normally I'd go raid a second planet for a wad a of titanium ingots with silicon on the side, but shout out to the Dark Fog for providing the ingots, free of charge (well...kindaa)
- Anyways, I'm going to leave it at that. So...with a single blueprint and 4 inputs...I got yellow cubes working at an ok starting rate. Of course one of those inputs is not really that available on this planet. How do I make do? Well that's for another video...Oh yeah trying to get through a large playthrough at the moment and maybe even a movie afterward...I can try to take that on and see what's it like. But if there's a build that you interested in seeing don't hesitate to let me know and I can see what I do. thank you for watching and I'll check you later.














- Where am I at 799? Still good...still working fine...
- Wait was that it? Everything down?





























- and a quick 10 minute test shows a yellow cube output of 90 per minute. ok.