---
date: 20241205
areas:
  - DysonSphereProgram
title: Standard Run
description: a type of playthrough where
updated:
---
# Relationships
- :  is [[Playthrough]] a short as possible, minimal side quest, efficient base buildup to whatever goal really, but usually it's the mission complete in [[DSP - 00]]

# Ideas
- 202502080820: [Listed the Technologies in Order] It matters which order the technologies are done in. It can be save alot of time when it is done right. Also, it will help for a particular lore video if I ever get to that.



# Notes


# Relationships
- 202412130736: [DSP - Technologies and Upgrades] are a [DSP Mechanic] where research points are given to a technology. Reaching the end of the technology will grant a permanent reward to the player in the form of an upgrade or building availability.



# Notes
- [DSP - Technologies and Upgrades]: List of -most- of the tech and upgrade tree


## Blueprint Que
- Making Charged Accumulators
- Inserting Engine, Red Motor, Blue Motor, and Processor into the Mall
- Plastic Stage
- Yellow Cube 180m (inputting refined oil since it's made already)







## Build Groups
NOTES
- Builds, factories, productions, whatever you call it, all the factories needed to complete the game are grouped into these categories.
PRE BLUE CUBE
- 3 sets of smelters (12-21 smelters) making [Iron Ingot 360m], [Copper Ingot 360m], and [Magnet 360m]
- Start Bus A Mall (12 assemblers) Gear x2 > Belt >Sorter (Splitter after Logistics lv2 > Wind Turbine > Tesla Tower > Assembler > Mining Machine > Gauss Turret > Blank (Sorter mk1 after Logistics lv2) > Red Motor (Sorter mk2 (logistics 2)
- 8 Assembler array for [Circuit Board 360m] and [Magnetic Coil 360m] (don't forget to run Iron Ingot to the rest of the mall...)
POST BLUE CUBE
- 6x Combustible Unit

POST RED CUBE
- [ ] 01:50:15 Proliferator mk2 ~190m

PRE YELLOW CUBE
- 02:24:14 Yellow Cube 180m
POST YELLOW CUBE
- 4:39:21 Processor 281m

PRE PURPLE CUBE

POST PURPLE CUBE
- 05:05:31 Purple Cube 180m

PRE GREEN CUBE
- 06:52:13 Green Cube 180m

POST GREEN CUBE

PRE ANTIMATER
- Solar Sails 576m
POST UNIVERSE MATRIX
- [ ] 2.34 GW (2:17:37)
- [ ] Solar Sail 1440m (2:27:04)
- [ ] Universe Matrix 134m (2:33:57)



## Posted Runs
- 20241216 - [3000% Playthrough DRONE ONLY NO FOGGER BASES](https://www.youtube.com/playlist?list=PLCvwzLfqkAHauo2ImAItmdNHUDV5vq48g) - some mods are used which I can't blame. 40ups exploit used. Still this is an amazing run that I'm glad was posted 36:00
- 20241102 - [3000% Playthrhough](https://www.youtube.com/playlist?list=PLCvwzLfqkAHalOh4fJWV2qKQCS15QGRDy) Mrrvlad's max difficulty run using drones. AND in less than 10 hours. What a beast. (file: 2025-02-25 07-39-30 Vlad 3000% Playthrough (2024)) (bookmark: 4:11:59)
- 20230414 - [Dyson Sphere Program 10h run, minimal resources. Random seed, no save, generic blueprints, 2x speed.](https://youtu.be/SWMeHkCGgqY) - mrrvlad run Gold standard AFAIK
- 20230302 - [Dyson Sphere Program Speedrun in 2:40:09 (Blueprints in Description, Segmented)](https://youtu.be/OhY8oNkxAW0) - ymblcza uses map specific blueprints and segments. Naturally, this is WR as far as I know
- 20230122 - [Dyson Sphere Program - Finished in Under 4Hrs!!! - Any% WR Speedrun 3:58:47 (segmented)](https://www.youtube.com/watch?v=S8D5IEzQe0c) - Toesies Tim has another go at the game and apparently breaks his own record!
- 20220830 - [Dyson Sphere Program - Speedrun - Mission Complete 5:21:00 (segmented)](https://www.youtube.com/watch?v=4_EJlOseHnI) - Toesises Tim sets a "fast record" segmented using map specific blueprints
## FactorioLab
- [Universe Matrix 360m + Solar Sails 360m](https://factoriolab.github.io/dsp/list?z=eJwlyDELwyAQhuF.c8M3RQJuN.QEA6XpkqWdMgSHkARB25I4-NuLutxz7-dZEnrdkWdx9XGMoZw7BbYeER0FlhVV-0EspIp5ttU8mgugSo4tZWw94UbBnfzGhi.OfEFDXpAdxmYz0HEEnunHSv0BiCQmjA__&v=11)
- [Yellow Cube 180m](https://factoriolab.github.io/dsp/list?z=eJwtys0KwjAQBOC3yWFAaAr-XPbgBtOLVgXFehKxpUiphbSK9tBnl2RzmY8ZpqM99CpRFSHzsfFhlSPboUeiHPETQTug94wBs5Z1EOYC19G7WAPa1weQKke5YHbRrTgK8cSz6DJaikWg-tIBF1xRosEbPyzAOfgMLsAN-AVOYexkMpjjZE6qbR3d1Ie0.gO1FjzU&v=11) - Proliferator mk2, input: refined oil, coal, 

## Discussions
- https://www.youtube.com/watch?v=daHDjQpK6Sc
- 202412121219 - [Tech Ids](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1hcq397/tech_ids/) - Internal list of techs can be extracted from dev console [https://dsp-wiki.com/Developer_Console](https://dsp-wiki.com/Developer_Console)
- 20220204 - [Dyson sphere program：A giant leap for mankind in 27:17](https://www.youtube.com/watch?v=HemE7WXa-Ok) - ymblcza speeds to the next planet as fast as possible

- 20221126 - [Dyson Sphere Program - Speedrun Red Cube (32:59) Random Seed, No Save/Load, No Premade Blueprints](https://www.youtube.com/watch?v=4xN9CqrqcVs) - Slychuu's Corner does a run as well described in the title. Doesn't he know there is a giant leap for mankind run?


## Log
- ************: so this game i forgot to route the iron ingots through the boards and then to the mall. Also the research que was wierd because of how far the coal patch was. Readjusted the que in the guide for next time.
- ************: Might as well log my take aways from each run attempt, because I'm not going to save the games that much.
