---
areas: DysonSphereProgram
date: '202307091953'
title: 
description: 
updated: 
---
# Relationships
 - is a [[DSP Players]]
 - is a [[Streamer in DSP]]



# Notes
- Extra product'ed -everything-
- Mk3 Sorters (only if needed)
- Tesla towers (only if needed)
- Planetary star power....doesn't need splitters

 

For Erickson

Extra product everything...even blue cubes, plane filters, etc...EVEN SMELTERS!!!

Mk3 sorters (only when needed)

Tesla towers instead of satellite stations

Pilers that are only needed in early game...of that.

Never heard of sushi belts?

Here was a clip of his stream
https://youtube.com/watch?v=694i4T4HpF0&si=2JtSWSISlRU9u5br



```Transcript 20240106
-    Home World Strategy: Focuses on building structures primarily on the home planet, using it as a central hub for operations. Discontinues certain builds after leaving the home planet.
    
- Warp Speed Efficiency: Utilizes high warp speed for quick travel, reducing reliance on extensive planning or construction on multiple planets.
    
- Resource Proliferation Approach: Builds slightly more resources than needed to speed up processes, preferring efficiency over perfect resource saturation.
    
- Resource Saturation Timing: Initially aimed for perfect saturation, which was slow, later adjusted to faster saturation methods to save time.
    
- Power Generation: Relies heavily on wind and solar power, minimizing the use of other power sources.
    
- Planetary Clean-Up: Occasionally revisits planets to clean up or reorganize, especially during less intensive gaming sessions.
    
- Modular Build Strategy: Constructs autonomous, self-sufficient builds with minimal interconnectivity, using autonomous agents for resource transport instead of belts.
    
- Resource Mall: Has a centralized area for resource distribution, connecting essential resources through conveyor belts.
    
- Proliferation Distribution: Spreads out resource proliferation across the planet, ensuring efficient distribution to various build areas.
    
- Game Mechanics Utilization: Uses in-game calculators and tools to optimize resource allocation and build ratios, ensuring effective proliferation and resource management.
    
- Decision-Making Criteria: Prioritizes certain resources for proliferation based on their value and utility, avoiding proliferation for cheaper, easily accessible resources.
    
- Expansion and Resource Allocation: Plans expansions methodically, using tools to determine the exact needs for specific builds, and adjusts resource production accordingly.
    
- Gameplay Balance: Balances intensive gameplay and planning with casual, less demanding tasks to maintain enjoyment and avoid burnout.
```




# Comments

## Styles
- If you have a goal like mission complete or x white power second as fast as possible then I agree with you . If your goal is to build bigger until you get bored then there is no such things as over building because you don't know when you're going to stop. Which is how most people play.
- The game judges us based on power generation in the galactic map. So like my 100tw game I only make 13k white because power is the point not white. I think I can get 50k white in 30 hours. I can get to the end of m y mid game at 20
- Right. I do. I use ratio of there's a time goal. if time doesn't matter then ratio doesn't matter. I fyo uptime doesn't matter then just build big
- We originally talked about good plans are determined by time and flexibility. If you ignore time then I don't know how you determine your plans quality. Given infinite time all plans are good. I can reach any goal in infinite time. My point is there has be a benchmark. I'm not a speed runner. I want good plans. And good plans are judged by time and flexibility.
- As fast as possible and works in every situation. This is the entire point we discussed. Now you say you don't care about time. My point is time ahs to matte.r YOu can't judge plans based on a goal like "get 50k white". Because you can hand crank to 50k white. You can't measure convenience. Plans can be judge with a goal like "50k white in 30 hours on a random seed" because that includes time and flexibility. You said earlier you don't care about time and I think that's a corrosive thought.
- Sorry for going so hard on this subject but you said earlier that you don't care about time and that leads to puttering around on yellow for hundreds of hours and we've decided that we're going to try to push the community to be better than that and so I'm just trying to keep you focused that weekend keep putting out crisp plans that focus on time and flexibility.
- My goal is mission complete in 10 hours then 50k science in 30 on any seed. Back to work for me.