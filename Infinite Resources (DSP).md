---
date: "202308050908"
areas: DysonSphereProgram
title: 
description: 
updated: 
---
# Ideas
- 202412071121: [Resources are Infinite in DSP] There is a position that resources are infinite in DSP and will thus never run out. Although there are some nodes that can be depleted in the early to mid game, I agree with this statement. Eventually thanks to 

- 202412071124: [Resources are Infinite in DSP - An Analysis] in 202106150740, Awesome_Avocado1 posted in [reddit](https://www.reddit.com/r/Dyson_Sphere_Program/comments/o0cbxz/all_about_veins_utilization_how_infinite_is/?utm_source=share&utm_medium=android_app&utm_name=androidcss&utm_term=1&utm_content=1) an analysis not only detailing when resources become practically infinite (I think the term is asymptotic but the game rounds it up infinite at a point anyway) but also the exact points of "inflect" there are formulas provided so if anything changes the predictions can be adjusted accordingly.

- 202412071122: [There is no Enemy in DSP] Just as there are infinite resources in DSP there is ultimately no enemy in DSP. Although in the early to mid game, there is substantial resources to counter and neutralize the threat, eventually thanks to researchable upgrades, they become almost meaningless as less and less things are used to beat them

# Notes
## Reddit
- 202501101757 - Memes - [Its just less to worry about](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1hyhijj/its_just_less_to_worry_about/) - "My starting system ran out of resources!" Me who plays with inifinite veins



## YouTube Thumbs

```vid
https://youtu.be/eHdhStV3Ml8
Title: 𝑰𝑵𝑭𝑰𝑵𝑰𝑻𝑬 resources are better? Dyson Sphere Program
Author: AYOPLAYS
Thumbnail: https://i.ytimg.com/vi/eHdhStV3Ml8/mqdefault.jpg
AuthorUrl: https://www.youtube.com/@ayoplays6497
```






