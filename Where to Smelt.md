---
areas: DysonSphereProgram
date: '202403241924'
title: 
description: 
updated: 
---
# Relationships
- A common question of where T1 production should be done on where the raw resources are collected, or should all the raw resources be transported to "predefined" T1 production.
- There is obviously more to the logistics question than just T1 smelting. Let's try to expand on it here.




# Discussions
## 2024-03-24

https://www.reddit.com/r/Dyson_Sphere_Program/comments/1blguk3/do_you_mine_on_separate_planets_and_smelt_at_a/

ZEnterprises: On minimum resources, I burn through planets. So I design from raw. A huge lightbulb moment- I never needed nearly as many smelters as I was building. It cuts down on interstellar shipping vs a forge planet. Easier to expand if Im not chasing bottlenecks.

calmclaren: So u have smelters in ur blueprints along w assemblers?

ZEnterprises: Yes, indeed. Once I got in the habit of balancing the recipes, it was just another step. Super easy.

calmclaren: Fascinating. I might consider that

ZEnterprises: O see three options. Each has its own purpose. smelt at the vein. Great for infinite resources. Smelt everything on one planet. Great for 1x resources. Smelt in the blueprint. great when you have to keep opening new planets on minimum resources. Im sure there are other techniques and reasons. These are mine.

calmclaren: Thanks for extra clarification, I'm on 1x and haven't tried the others!

idlemachinations: I have a mixture. For components produced in large quantities typically representing a compression of resources, like processors, green motors, casimir crystals, strange matter, graphene, plastic, and such, I produce them on the planet where resources are mined or on a satellite orbiting a gas giant providing the resource. I then have central planets for producing mall items, white science, rockets, etc which import these intermediate products as well as raw resources for everything I do not have satellite factories for. This is the strategy I find most intuitive and enjoyable. I know plenty of people employ smelting planets or black box designs because they find those strategies better for them.

12lo5dzr: What is a black box design?

PhoenixNZ: Input raw materials at one end, get a finished product at the other. Eg for orange processors, you just input the iron ore, copper ore and silicone ore, and the design gives you the orange chips at the end. All intermediate processes such as smelting and crafting the intermediate products is done.

idlemachinations: A black box design is a factory which takes in raw resources and produces a finished product, such as white science or rockets. All production is contained within one design with no requirement for other factories, only miners. Exceptions may or may not be made for power, fuel rods, warpers, or proliferator. The benefits of such a design are that troubleshooting is easy. Either you have enough ore coming in or you don't, in which case you build more miners. If you do not have enough output, you stamp down more black boxes until you have enough output. There is no chasing down multiple factories and complicated logistics to figure out what your bottleneck is. The drawback is that I like logistics. Also, these setups can be quite large and difficult to design, then you need to actually transport all those raw resources to your black box design, which can be a challenge if your ambitions outpace your logistics capacity.

chargers949: https://www.dysonsphereblueprints.com/blueprints/tillable-blackbox-module-raw-green-cube-wrapper-mkii-iii-1-s-square Input raws and out comes green cubes and warps. I drop one of these every star system.

PhoenixNZ: Titanium, silicone and coal are all worth doing on the planet as they are two to one ratios, so you have to transport less. Everything else it really makes little difference IMO.

Ok_Confection2261: I just do black box so I can just paste it everywhere and not worry about bottlenecks other than raw material supply

XsNR: Bit of both. They usually have a rich side, and a less rich side, or hemisphere. So start with that, and once it's a mined out husk, it becomes a full production planet, and we move onto the next, the factory must grow.

hex00110: I have a mega-planet design that is mostly balanced — it does all of the smelting — so I just mass-mine resources from planets and ship back to my 2 mega-factory planets


QDoosan: The only raw I transport are the rare ores.

Starcaller17: I do on planet smelting early, but once my vessel speed is like 20-30 I make forge worlds and export raw ores. Except spiniform. Always smelt those into nanotubes first cause they are so bad

sage_006: Titanium and Silicon, I always smelt on site, as it saves trips with logistics carriers and there is nothing (that I can think of atm at least) that requires raw Titanium or Silicon. Other raw materials I guess I do a mix of depending on the infrastructure I already have in place. Though I always play on infinite resources so i dont have to out source raw materials very often.

NormalBohne26: could also do for copper since nothing requires raw copper ore but copper plates

sage_006: It's true. But copper is a 1 to 1 smelting ratio. So it doesn't change the number of vessels needed. It does save space on the factory planet. But yeah. Other than rare ores, smelting everything onmining planets isnt a bad call.

BTG412: I do both, I transfer everything thing out to a main hub and then build out smelters on the easy stuff. My main home planet needs the little bit of ore to keep everything going so I all ways have it on hand to send back to my original planet

BabyMakR1: Mostly I mine and smelt on the same planet. However, if the planet is a bio planet (has oil and coal etc) I don't do manufacturing on that planet because I don't want to have to bury oil seeps etc, so I export everything. Later I'll go back and the ones from early game will have run out of resources so I'll pave the planet and turn it into a manufacturing planet.

Unbelievablem8: I place smelting setups with ILS independently of resource sources and export raw resouces from planets. It makes it irrelevant if resources on that planet run out - the setup will still keep working if you have enough of raw resource supply anywhere in the universe. I prefer to focus on scalability, reducing cognitive load and UPS efficiency rather than logitstic or energy efficiency.

lionexx: Depends how far in the play through, generally I do a mixture. I am a big fan of having a central planet where most things are created in a massive polar hub for “shipping” or easy access that I know I can grab stuff from knowing exactly where it’s at and having a forge world do the bulk of crafting for sub components of general or high use items, and will strip planets for what I need. I think most people do this or something similar, minus probably a forge world.

knexwiz13: It is harder to manage the numbers and I should probably do a forge world or worlds but I tend to smelt where I excteact. Extract what spots I can, set the adv miners to full blast 300% , do the math to get outputs of 7200/m and put down my corresponding smelter logistics stations blueprint. Made of 1 ILS, and 60 plane smelters, or more smelters depending on the resource. I have one for each raw that can be smelted down, even some rares. I went back and mass deleted my old outdated smelter stations that only did 1800/m. I have the SLS pick up raws with bots but leave the interstellar ship spot empty, have those on the demand ILS.

douglasduck104: Just started using a forge world in my latest save - I've greatly appreciated the ease with which I can just setup miners on systems on the edge of my galaxy and not worry about setting up smelters at the same time. It's also stopped me from looking for 'goldilocks' systems where I have all the basic and rare ores I need to produce specific end products and skipping over the ones that didn't have some of them. The main problem I've found recently is that I can't keep track of how much I actually need of each type of smelter. I thought I had a good ratio but I definitely didn't have enough silicon going through. I'm just building more rows of forges for now but it's probably wasting quite a few extra buildings. It might be easiest to just mine ores whereever then smelt as required in-system, not caring about the actual ore availability on each planet. The main thing to be careful of is to decide what you are doing and stick with it - ie if you ship ores via warper and then move the smelted products around in-system without warpers, then you must not start supplying or demanding any smelted products in an ILS with warpers because of the ILS priority order not differentiating between in-system and inter-system.


Takyz: After I achieved warp travel I mine on all planets that I land on then install logistic towers to collect the resources then get them shipped out to my forge planet located on a different system then the processed materials are shipped to my assembly factories that are located on a different system, nu doing so I noticed that I have better FPS towards end game rather than just cramped everything on one planet or 2 in one system

Kholdhara: if you smelt on the planet you mine, you reduce stress on your system by having too many ships traveling all the time. Of course that is if your system isn't strong enough to deal with the stress. otherwise i generally try to make smelt from the source given that the products can be called by any planet that needs it, whereas raw ores are only needed by smelters.

casey28xxx: Personally I think it’s more efficient to smelt on the same planet you extract from, but I guess in the grand scheme it doesn’t matter that much.

Jochi18: I like to produce at least the first material on the planet, let’s say Iron. Many times I like to produce the next tier product as well. Example if I have Iron and Coper I make Circuit Boards, Gears, Magnets and Magnetic Coils. So I rather have some refined low tier materials ready in several planets.

voarex: I normally do all thr smelting in the starter system. Then when scaling up white science I start picking a planet for a color or two and do its smelting there. It cuts down on the wrap/power usage because you will have raw resources and cubes getting warped.

ArtisticLayer1972: Yes

Steven-ape: No. I don't want a smelting planet, because it creates too much interstellar travel and another location where bottlenecks can pop up. I ship ores and I smelt where I need the material.

hobolobo2022: All 3 of my starter planets have been transformed into forge worlds. I pump in resources from dozens of planets and pump out what I need to keep expanding. I have 8 storage planets throughout the stars so I can pull resources quickly from almost any point of the galaxy.

InfiniteCrypto: Mining and smelting on same planet, ship to assembly planet to make components and to homeworld to make high tech stuff. Also separate chemical refinement planet