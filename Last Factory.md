---
date: 202307300107
areas: DysonSphereProgram
title: 
description: 
updated: 
---
# Relationships
-  is a [[Problem]]



- On the last machine that outputs onto the belt, also have it output onto another belt that sideloads onto the main belt. Whenever there's a gap, items from the side belt should pop in and fill up the empty space.
- use t-junction belt to hook up the last assembler(s)
- somewhat higher performance than blue sorters
- [[Conveyor Belt]] for further notes on the mechanic