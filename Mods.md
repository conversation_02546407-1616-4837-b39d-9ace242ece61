---
date: 202504130912
areas: DysonSphereProgram
title: 
description: 
updated: 
---
# Relationships
-  are modifications to the game [[DSP - 00]]

# Notes

## Reddit


# Log
- 202504251400: [Using DnSpy to See Code of Unity Games] I have so far been able to use DnSpy to reverse engineer some parts of Dyson Sphere Program (it's a Unity Game that the devs have nicely allowed for easy reverse engineering for modding). I have a feeling that is the actually the easiest part of this whole process.
- 202504250310: [DSP-HUD Mod] Create a mod for the game Dyson Sphere Program. It is a configurable HUD based on the dashboard view recently added to the game.  (1) I want this mod to create a view that copies all of the widgets that would be shown in the dashboard including all widgets that would be shown.  (2) Except in this view the dashboard widgets are completely locked. Indeed the only way to edit the widgets is in the normal dashboard view (3) Also background is completely transparent, the player can see through the background. The widgets can still be seen like normal. This is why the mod is like a HUD.  (4) Finally, let the player perform all normal actions like moving, combat mode, configure stations, ect. The widgets are to remain in place in front of the background but can be placed behind any other windows called by the player, like stats and stations.
- 202504241501: I think I can reverse engineer the bottleneck mod. That's a good starting point to figure out how stats are created and parsed in Dyso. Sphere Program
- 202504131340: I took a look at Appuns Station Info Mod. This was the very first mod I used so naturally it will be the first for me to reverse engineer. Appuns of course put the uncomplied code in github [Link](https://github.com/appuns/DSPStationInfo) from there I can the code and try to understand what it tries to do. In theory this should be the minimum amount of learning I have to do before I start my Dashboard Mod.
- 202412140451: [ChinaShopBully is a Mods Guru] almost every reddit conversation that I see in Dyson Sphere Program that involves mods, ChinaShipBully is always there to answer general questions about mods! It's like he scours the reddit and will jump at ANY question or conversation about DSP mods. Of course, he has a ready "list" of the best and current mods he recommends.



