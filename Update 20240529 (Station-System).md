---
date: 202407050100
areas:
  - DysonSphereProgram
title: 
description: 
updated:
---
# Relationships
- 202405301943:  is a [[Game Updates]]

# Ideas

# Notes

## Script
So I will probably be using ONLY the point to point and here's why

In my day job my boss always say define the objective...what are we trying to do here? What is 

one particular of Dyson Sphere Program as a factory game is that the items produced at a certain tier can be mixed.with other tiers. For example you made nanotubes for broadband. Great! Is that it! No of course not! You'll need them for...well...Sphere materials...and stuff

O k well let's get this over with because i'm old and my back is killing me right now

Tilt belts? I'm not sure what the means...going to have Have to look into that.

BAB pick up speed improved: By only twenty five percent I wonder what they were thinking when they made that change.

Disable sandpile dropping. No, why would I want to do that? I want more more...

read the height of conveyor belts: Oh my God, yes. How many times I've gotten confused of my own belt builds?Actually I can show that

Free island parlor in sandbox mode: I don't quite remember what I had to set every time I had to load a sandbox save.I guess that was it.

Navigate tech tree with w a s d keys: Oh my God, this is the start of event.Thanks , it's thanks and place its sphere programme in vim mode Just remember to put those w a s d keys for nudging blueprints ( I think Tuco heard me)

Optimize document positions at b a b: No idea what that dies

on screen limit for traffic monitor: I suppose we want to have a while planet of them why not


# Relationships
- : 

# Links
- Developer Updates on Steam: https://steamcommunity.com/app/1366540

# Log
- 202407300534: I'm going to have to shelve covering the Station Update on YouTube for now. I've been dealing with more noteworthy projects (Rust and PKM) and even the [DSP - One Planet Challenge] has to be put under the radar