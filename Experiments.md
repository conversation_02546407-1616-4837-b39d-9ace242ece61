---
date: 202502252213
areas: DysonSphereProgram
title: 
description: 
updated: 
---
# Relationships

# Ideas
- 202502252214: [Does Ground Tele-Sorters Ignore Twisted Belts?] I have so far noticed 4 for 4 that teleporters ignore touching an existing ground, a serious bane to using teleporters. Perhaps it ignores it because it failed the angle of connection check. Either way if this is true we have a truly one step blueprint that includes both ground belts and telesorters up to 1.5 grid making a complete 3 item in one grid possibility!

- 202502252213: [I Think Using the Slowest Sorter is Best UPS Efficiency?] There is a theory that sketers are more optimal when they are "moving" as opposed to "waiting". Confirm this theory

# Notes




