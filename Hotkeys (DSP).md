---
date: ************
areas: DysonSphereProgram
title: 
description: 
updated: 
---
# Relationships
- ************:  are a list of hotkeys and their "contexts" that they are used in. Categorized under [[Guides]]
# Ideas
# Notes

## Hotkeys
### Views
- M: planet view
- Ctrl + C: blueprint view
### Panels
- C: Mech panel (mech inventory)

### Mech Panel
- ctrl + lclick: empty tile / click button reorganize

### While Placing a Miner/Pump
- Shift: Ignore Grid Snapping
- Shift + R: (placing miners) finer rotation, like...ignore grid snapping

### While Placing a Splitter
- Tab: change splitter type

### While Placing a Belt
- R: change belt pathing mode

### While in God-Mode
- Shift + rclick: mech automatically paths (kinda dumb though) to the point that was clicked on
- Shift + WASD: mech moves accordingly

### While Placing a Foundation, Deconstruction, Upgrade/Downgrade
- +/-: change area size (foundation, deconstruct, upgrade/downgrade)

### While Hovering Over A Building
'<': copy building config (usually the recipe, but there are other properties that can be copied)
'>': paste building config (usually the recipe, but there are other properties that can be copied)

### While Hovering Over a Belt
- Shift + lclick: deletes entire belt segment
- Shift + lclick: copies a building (usually with sorters attached)
- " ' ": collect item off belt. Hold down to continuously collect







+/- (building zooping) change building spacing
ctrl + ldrag specify ammount to remove (you can type the specific number as well)



on a building
tab fill its buffer/inventory with compatible items

on a belt
~ remove items from belt

while in blueprint mode (Ctrl + C)
Shift + rclick move Icarus around (including flight mode)
Shift + lclick force paste blueprint (will ignore all red/collided buildings)

While in planet view (M)
Z pick up items (the area covered is greater than in normal view)
N orient north pole facing up