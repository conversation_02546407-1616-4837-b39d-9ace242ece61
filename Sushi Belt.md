---
date: "202306131303"
areas: DysonSphereProgram
title: 
description: 
updated: 
---
# Summay
- 202307130829: A  is a [DSP - Conveyor Belt] containing more than one type of item.

# Ideas
- 202412100536: [Elevated Sishi Mixers To Save Room Perhaps] I think I've seen [<PERSON><PERSON><PERSON>] do this; he would put the entire sushi mixer and elevate it above the assemblers! Recently it has been easier to design such contraptions. The motivation here Is to save room since complicated and scaled up mixers take up significant ammounts of space.

- 202409261954: [Reasons to Use a Sushi Belt] There are two reasons you want to try a . One, you want to use less belts, and the other is you want to use less sorters. The more of those items are working out there, the more work your computer has to do make them, so we want to improve the [DSP - UPS Optimization] as much as possible.

# Notes

## Reddit
- 202210071123 - [Is this Sushi Belt Optimal?](https://www.reddit.com/r/Dyson_Sphere_Program/comments/xy1xwc/is_sushi_belt_ups_optimal/)
- 202406142109 - [What is a practical use for Conveyor -> Conveyor Sorters?](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1dg6cfk/what_is_a_practical_use_for_conveyor_conveyor/) - A picture of "sorter jumper" one person said it's a limiter for sushi belts
- https://www.reddit.com/r/Dyson_Sphere_Program/comments/1dg6cfk/what_is_a_practical_use_for_conveyor_conveyor/
## Script

It practice, you are too early in the game to even bother with saving anything. Especially belts and sorters.

And in late game, maxed out belts amounts are all the same, whether or not they are sushied.

So point one is kinda dumb.

Eat your heart out speed runners!

Point two is definitely an option

Would you take a large production clocking hundreds of factories and multiple sorters each?

Or hundreds of factories with single sorters on them?

I thought so



Now if only there was a way to downgrade these items. An additional way for all the fast sorters to pick up all the items from the downgraded belts.

[fade in the automatic piler, with deep heartbeat sounds, kek]


Hook:
Hello viewers, you’re watching an episode of Dyson Sphere Program Let’s Build guides, build strategies, mechanics, news, theory crafting, an

In one of my episodes I covered a specific factory build that nonchalantly mentioned something not mentioned much in youtube sushi belts. So its about time we went through that in full.

Introduction:
Today is going to be a long one, we’re going through a few build strategies centered around sushi belts. For those of you who don’t know, sushi belts are conveyor belts that contain more than just one type of item. They are then passed through many facilities that can pick out one or more or all of the items in said belt. Just like an actual sushi belt. 

Hopefully after this video, you will have better knowledge of how they can be made in DSP, as well as their pros and cons. Personally, I think it’s a great factory building strategy to explore and play around with.



To summarize, this sushi belt implementation works with three essential parts. First, the inputs are mixed together to the desired ratio with splitters. Second, the items are sent through the factories which are picked up with mark 2 sorters. And third, items are not picked up by sorters are sent back to their source are resorted and.



SPLITTERS VS SORTERS

CHAPTER 1: OVERFLOW

To start let’s go back with that Dyson Sphere Components Layout

As of this video, there is no way to stop mixed belts that are lacking in one item. Therefor

But if you just want to check it out in-game then I put a link to the blueprint in the description

CHAPTER 2: INPUT/OUTPUT MIXED BELTS

To be honest, I was so incredibly impressed with the capability, I should deserve its own episode. They all deserve their own episode.

CHAPTER 3: AUTOMATIC PILER

This is an early game option

CHAPTER 4: LOGISTICS BOTS


Goodbye:

Description:
Some new ideas have came about since showing the 9,000/min SOverflow Towers, Automatic Pilers, and Input/Output Sushi

Skill Level: ADVANCED
Resources Level: ????

Introduction
Greetings, and welcome to Dyson Sphere Program, where you can make some pretty crappy spagett and still have a nice factory!

Tell me, do you like belts? Like really really really like them?

Did you watch my previous video? Did you like sushi belts?

Ready to go next level? Oh boy do I have a dooze for you!

Behold…sushi belt!

Well that was anticlimactic

But seriously hear me out…you have a factory. It takes in input. It takes out products.

Now I’m going to guess very few of you have seen a single belt with multiple inputs.

But I’m going to guess that even fewer of you have witnessed a single belt of 

Proposal

We start with our factories…they put in inputs and spit out outputs.

First we have an input belt…nice and full at the start…pretty much empty at the end. Sad

And then we have a separate output belt…full of stuff at the end…doing absolutely nothing at the start. Also Sad

Why would this break?
Here’s the scenario. 

Let’s suppose you have a single factory with a

single input sorter, 

a single output sorter, 

and a single belt running through it. 

That single belt is full of items.

For this to work the input sorter grabs an item, creating an opening in the belt

And then the output sorter plops down an item into the belt, right on that very opening.

It doesn’t matter what is beyond it (it’s too late for the factory to do anything anyway)

and it doesn’t matter what’s before it as the belt is going to be full…one way or the other

A factory can’t make items instantaneously. 

The first time the factory picks up an item that space…is already gone.

BUT once that product is made it is put into the output sorter. Ready to plop down when that space is available again.

AND hopefully, that input sorter has space in the factory to put an item to pick up.

You see where this cycle is going? whether this works or not is going to depend on what KIND of buffer it has on it

The belt is going to full, the factory input is full, the output is full, It is output locked…FOREVER

So we need to look for ways it will grab an item the moment an output is made…how do we do that?

The Logic
If you been watching or reading about sushi belts, you would know that green sorters have a very funny input logic with piled belts.

First, the amount of input that can be automatically loaded into a factory is the recipe amount times 4??? CHECK So if the recipe calls for 2 items sorters can autoload it up to 8
The funny part is the mk2 sorter will NOT EVEN PICK UP an item if there is no room in the factory. Noticed by this idle state

Oddly enough, this includes piled items. If it would over that x4 limit…it’s not happening 

The way is works is it comes in here

And then sometime later it comes out there

Now if there is no room in the belt to put a product then the line will start to back up

HOPEFULLY before that happens it’s able to pick up an input

Create an opening

And drop the output

The Logic (Advanced)
Assuming it can output forever and instant transmission the factory will have input items at 6 > 8 >6 > 8 > 6

Sadly the output sorter does and will grab an item from the factory, even if there is not a space.

So it can try to put an item in the space while there already 8 items in the factory

Not good.

How do you make have a product to go  

Is this more UPS efficient?

RIght from the looks of it the old layout takes up about XXXX belts. The new layout is taking up XXXXX belts to get going. As of this writing, each belt segment would take about XX nm or so on the tick. That’s a alot of belts unneeded and now we have a great 

REFERENCES:
Yet Another Sushi Belt Explanation, Except with Blueprints
"Rated" Sushi Belts: A Primer (AKA: Cargo Stacking Sushi Belts)









# Notes
- 202408272002: There has to be pictures for these mixers. The syntax I made here only made sense when I made them, but now it's just hard to follow, much less repeat on.

## Sorter Rates of Pile Sorter
Note: The rates will depend on the pile sorter upgrade. They all go up to 1800m (full belt) when fully upgraded, regardless of distance (up to g3 afaik) once upgraded they are NOT reversible
PSU0 g1 = 800
PSU0 g2 = 480
PSU0 g3 = ~333
PSU1 g1 = ~1005
PSU1 g2 = ~425
PSU1 g3 = 360



## Sorter Rates By Tier and Range
T1 g1 = 90
T1 g2 = 45
T1 g3 = 30
T2 g1 = 180
T2 g2 = 90
T2 g3 = 60
T3 g1 = 360
T3 g2 = 180
T3 g3 = 120

## Sorter Rates by Increasing Amount
T1 g3 = 30
T1 g2 = 45
T2 g3 = 60
T1 g1 = 90
T2 g2 = 90
T3 g3 = 120
T2 g1 = 180
T3 g2 = 180
T3 g1 = 360

## Sorter Rates by Increasing Amount (4x Pile)
T1 g3 = 120
T1 g2 = 180
T2 g3 = 240
T1 g1 = 360
T2 g2 = 360
T3 g3 = 480
T2 g1 = 720
T3 g2 = 720
T3 g1 = 1440

## Sorter Solutions
3600 = T2 - T3 - T2 - T2
3600 = T3 - T3 - T2
3600 ./ 3600 = T2 - T3 - T3 / T2 - T3 -T2 - T2 (replaced the final T3 with double T2)



Here’s a 9000/min processor layout. The basic idea here is the remainder of 4 sushi inputs [belts] go into a 5th one in the center row.



The amount being inputted is meant for 240 assemblers. However assemblers #240 and #239  fail to pick up the remaining input and therefore stall a small but noticeable amount.


I’ve tried a few things: resetting the inputs, slowing sorters, additional input sorters, even reverse piler in an effort to make it easier for those last assemblers the pick up stuff. 

The best result seemed to be to downgrade the belt going to the last 12 assemblers to mark 1. This improved the output to almost perfect, lol.


## Blueprints
- 20231227 - [Tiny four-way sushi rebalancer by Steven](https://www.dysonsphereblueprints.com/blueprints/factory-tiny-four-way-sushi-rebalancer)


# Log
- 202503301723: (from 202503301611) I'm closing up this experiment. It usually works fine with some kinks here and there. There is no auto-correction especially early factory if the output sorters are overflowed. The secret here is to have a less the full input but we'll explore later.

- 202503301611: (from: 202503301211) going all out now! I now collecting the excess input properly I think, and ALL factories are using output pile sorters they will now jam strange matter if they have to into the belt. I need to next jam the output and even jam the factories to see if they can work again from a jammed output state.

- 202503301211: Restoring particle containers after removing it for 10 minutes. There are gasp in the main belts which indicates it will be restored to full throughput of ~1550m

- 202503301145: Even with double sorters on the last factories, there is still difficulty but there is some improved efficiency to picking up all the items passing through the main belt. I will choke each of the items and see if they automatically start back up.
- 202503300939: [Full Belts are Tricky With T3 Belts] T3 Belts are moving items at nearly 30 cargoes per second! Due to the speed a sorter that has to fill those last gaps, may have a tricky time doing so. So it becomes critical to test around with different configurations that give the right ratios of full belts.

- 202503300913: (from: 202503300852) I am testing the belt now. The issue I'm starting to see is that the input belt is normally "full" at the start. If there is an output jam, all the factories will get input full, and the belt becomes full of input and there is no spot to output to. I wonder who the splitter will help with this.

- 202503300852: (from: 202503300658) thinking more about how the splitter works, it will simply remove the output from the main belt, just like a sorter would, however the difference here is if the output is already full, the splitter will stop the main belt, a sorter will just let the output keep passing ina

- 202503300658: (from: 202503300652) before I continue I should add that this is "technically" a [Strange Matter 1B] setup. All I did was painstakingly mirrored it without mods (mirror blueprints when). Besides looing cool, both productions are sourcing from the same station. (Especially the deuterium...the 5 odd belts are split between the two halves) (note: I have a few working in my old save in Monika III)

- 202503300652: (from: 202503290842) I'm going forward with [Strange Matter 2B] since the cycle time is very long, the belt is long, and the numbers support it, I thought I can mix iron ingot particle container input with strange matter output. This initial setup is little outdated because the inputs are mixed together with splitters, which is already prone to jamming when an input is missing so let's fix that a bit...

- 202503290842: [Discovered Unjammable Input Output Sushi] I think I did it this time. The key is placing a output splitter right before the inputs are inserted in. If the outputs start to overflow even a little, most if not all the entire belt is jammed, preventing it from permanently saturating the (single) mixed belt. When output is carried out, the belt is free for inputs to continue inserting. Would love to see the extent of this or even a splitter version (higher UPS) version of this function.