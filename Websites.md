---
date: 202503091031
areas: DysonSphereProgram
title: 
description: 
updated: 
---
 
# Relationships
- websites for [[Dyson Sphere Program]]
 # Backlinks
```dataviewjs
// Get all incoming links except a self‐link
const backlinks = dv.current()?.file.inlinks.filter(b => b.path !== dv.current().file.path);

if (!backlinks) {
  dv.paragraph("Error: Unable to access current file.");
} else if (backlinks.length === 0) {
  dv.paragraph("No backlinks found.");
} else {
  dv.list(
    backlinks.map(b => {
      // Load the page object for this backlink
      const page = dv.page(b.path);
      // Look for a field 'description' (frontmatter or inline)
      const desc = page?.description ?? page?.file?.frontmatter?.description ?? "";
      // Build the line: [[path]]: description (omit colon if no desc)
      return `[[${b.path}]]${desc ? ` - ${desc}` : ""}`;
    })
  );
}
```






# Websites
- 202410271702: 
# Log
- 202503091316: [I Would Like to Have My Own Website] How much money can I earn from a personal website assuming traffic of 10000 visits per day? Wpupd this revenue help "support" more hours into making resources? Basically I will be after making my own website that will be a repository of my markdown notes for a game..but I'll add graphics of my own making as exclusive content for the site...



