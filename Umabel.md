---
date: 202307130911
areas: DysonSphereProgram
title: 
description: 
updated: 
---
# Relationships
- 202307130912:  is a [[DSP Players]] 

# Ideas

# Notes
- YouTube: https://www.youtube.com/@Umabel_
- Twitch: https://www.twitch.tv/umabel_
- Twitter: https://twitter.com/Umal3el
- YouTube: https://www.youtube.com/channel/UCvvpeoJpvAxD4OnXc1myMcQ
- Discord: https://discord.gg/gBJWC8MrtT
- Official DSP Discord: https://discord.com/channels/750553061369577492/750554079037620245
- DSP Blueprints: https://www.dysonsphereblueprints.com/


## Twitter Signature
Dyson Sphere Program
@DysonProgram
/#DysonSphereProgram

## YouTube Signature
Links:
Twitch: https://www.twitch.tv/umabel_
Twitter: https://twitter.com/Umal3el
YouTube: https://www.youtube.com/channel/UCvvpeoJpvAxD4OnXc1myMcQ
Discord: https://discord.gg/gBJWC8MrtT
Official DSP Discord: https://discord.com/channels/750553061369577492/750554079037620245
DSP Blueprints: https://www.dysonsphereblueprints.com/


Game Mods [as if I can make them :(  ]
----
( ) Always on top stats
( ) Vessel labels
( ) Move on planet view
( ) Select group of stations and get logistics info / power requiremetns


SLOBS
---------
( ) production panel crowd control - give chat control of production panel?!
( ) "share" second screen procedure
( ) starting (countdown) timer

( ) improve "afk mode" by alternating views or give chat controls

OBS
------
( )

CHATBOT
------------


( ) chatbox and icarus "muted" speech/chat improvement???
( ) streamlabs cloudbot - give list of commands
( ) about page - socials
( ) find way to easily change name of celsital body
( ) get an avatar (tells people I am acutally on LOL)
( ) streamlabs desktop - quelist background
( ) fix mods used scene...make it look better!


MyTTS - Speechchat
-------------------------
( ) ...
$username$ said

MyTTS - AI?
---------------
Easier way to make samples?

DSP Blueprints.com
------------------------


This blueprint uses teleporting sorters (introduced by Ludus Machinae)

*This blueprint uses conflicted teleproting sorters in that they connect to elevated belts above despite being in contact with a ground belt. 
To ensure they all work:
1. Remove all mk2 belts from inventory
2. Paste blueprint...construct everything except for mk2 belts
3. Upgrade mk2 belts to complete construction



Twitch About:
Tag: "I play Dyson Sphere Program. I'm kinda good at it..." -Umabel 2022

About:
Greetings...I'm Umabel, a quiet PC gamer with a strange addiction to Dyson Sphere Program. Equilibrium?  Mechanics? Visuals? Expanding? Just learning to play as we go? Step on in and let's find out together what makes a "good game". 

It's difficult to  stream long hours 

Subscribe:

Donate:

Socials:

Rules:


CHAT RULES
----------------
Easy rules, just follow along
( )
( ) No slurs of ANY kind
( ) DO NOT trauma dump
( ) Please use emotes for jokes that can be easily misunderstood
( ) No religion/politics


Just a Poem
---------------
My Flower
There's my flower. It took a while. There are many but that one's mine.
I wonder why.
Not my favorite color. What are favorite colors anyway.
Looks calm? So is everything.
So many colors. Even black.
Why is that flower mine? I don't know. And I don't care.
It's just my flower.



/#DysonSphereProgram /#LetsBuild

Dyson Sphere Program is a sci-fi simulation game with space, adventure, exploration and factory automation elements where you can build your own galactic industrial empire from scratch.

Every playthrough will be unique: your universe will be procedurally generated every time you start a new game. There will be different types and distribution of stars, planets and resources. Will you manage to thrive and build your Spheres, no matter what the universe throws at you?

As a space engineer, you are expected to design your interstellar factory and production lines, not to micromanage every small package going back and forth. You have to transport materials from one planet to another, forming interstellar transport teams that gather resources and bring them to where they are needed.

Then, your resources can be transported between facilities through conveyor belts, and you’ve got the technology to help your buildings fit the grid automatically during the construction process. You’ve got the best tools COSMO can afford to build a massive-scale automated production line – the most efficient one ever seen in the universe!

- Build a galactic industrial empire from scratch: start with a small workshop and improve it until it spans the whole galaxy
- Develop your very own Dyson Spheres, a megastructure that orbits around a star harnessing all its power and energy, from the first screw to its completion
- Explore a vast universe procedurally generated with all kinds of celestial bodies: neutron stars, white dwarfs, red giants…
- Gather resources in planets of all types: ocean, lava, desert, frozen, gaseous planets…
- Research new technologies to improve your factories… and discover the secrets of the universe
- Enhance mecha fly, sail or jump through outer space and planets
- Transport materials across the galaxy to your facilities: thousands of transport ships will flow endlessly to your factories and back!
- Design the most efficient automated factory and production line
- Customize your factory and Dyson Sphere to make it unique
- Design a balanced power network capable of producing energy in all kinds of power plants like wind turbines, artificial stars, etc.

Blueprint: https://www.dysonsphereblueprints.com/blueprints/factory-dyson-sphere-components-9000-min-sushi-inputs
DSP Calculator: https://factoriolab.github.io/
Twitch: https://www.twitch.tv/umabel_
Twitter: https://twitter.com/Umal3el
DSP Discord: https://discord.com/channels/750553061369577492/750554079037620245