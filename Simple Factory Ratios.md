---
date: 20221207
category: dysonsphereprogram
title: 
description: 
updated: 
areas: 
---
Why yes. I think I can. Whatever math process you adopt, you do it enough times you start remember the process down more and eyeballing become easier or you make up more efficient ways some people use calculators and spreadsheets as it's quicker for them to do and remember (once you get the hang of it)

One way to start is to look at a factory rate directly: Once you make your pilot factory and it's working, look at the cycle/min by clicking on the factory. It considers spray and factory tier so some of the math is done there already.

unnpiled max belt rates are 360, 720, and 1800 /min for mk1,2, and 3 respectively. To figure the ratio of the factories to the max belt, divide the belt rate by the stated factory rate. Ex: if a factory rate is 60/min then up to 1800/60=30 factories can output into a single belt.

Same thing for factory ratios, just divide their cycle/min, although you have account for recipe ratio as well. Ex mk1 spray is 360/min and mk2 spray is 180/min though the factory ratio is still 1:1 here because mk2 needs 2 mk1 spray in the recipe.

