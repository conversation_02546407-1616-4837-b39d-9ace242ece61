---
date: 202405140907
areas: DysonSphereProgram
title: 
description: 
updated: 
---
# Relationships
-  is a [[DSP Players]]
- is an inventory of the [[Box Method]]

# Ideas
- 202502091358: [Mnemomeme Streams are Characterized as Chill But Very Tense Political Moments] MOST of streams are quiet. Nice and gameplay is entertaining. But then he speaks...MOST of the talk is VERY pretenious,  talks like a know-it-all, sometimes borderline rascist and misogynist. 

- 202502091404: [<PERSON><PERSON><PERSON><PERSON><PERSON> may be dealing with Schizophrenia] He mentions FBI, KGB, Gangs...all hating his guts and plotting to this day to do "something". 

- 202502091415: [<PERSON><PERSON><PERSON><PERSON><PERSON> adopts the Box Method] In one of his early games, <PERSON><PERSON><PERSON><PERSON><PERSON> runs out of iron. I pretty sure this is a common occurance in a typical gameplay, but he went on to blame "belts" for the issue. Therefore, to save on iron, he would avoid making belts altogether, spawning an entire system of practices.

# Notes
## Talking Points
- Just a bunch of things that I heard mnemo talk about when he's talking.
- Box method at all costs?
- Uses "solar panel in the equator" as a path to throwing hate toward someone
- Asks for appeal of speedruns (neglects that one appeal of speed runs is the skill required)
- Admits some faults of methods, but blames their faults on "bugs" and "certain devs out to get him"
- I suggested ways to make box method more viable. Says no changes is needed, just make sure "demand is full"
- Critizes people for afk running and then asks aloud the apeal of speed running
- Flagrant attempts at backseating on other channels
- Derogatory names : re---d, asp---, au----, 14yo boys who like pokemon, stupid, theif, lier, two-faced pos, mf, ah, espeicallly
- Calling out toward certain people, another streamer, a person with connections to dev (is that even true?)
- Gave an interesting website: curory.ai.org
- 20240202 - Well, to you guys' benefit, they improved belts in a dozen ways and added lube to the game because all of you hated the fact that box method was legit a thousand times better than belts, hands down.
- 20240202 - They were on my team, but there's a lot of you guys and they want to appeal to a wide audience. Letting me just win and embarrass the PhDs by optimizing even better than them was losing a lot of face.
- 20240202 - I mean, I'm only using like 200 dots of belt for a planet-wide build, as opposed to fifty thousand.
- That saves me whole planets of motor production
- Energy suck isn't important at that scale. My maximum energy on a single planet was around 28GW, and that's 95% production facilities, which I can pack tighter because I'm not using space on belts. It's a lot of differentials and combinatorics to explain all the math so most people are just scared of it because th4ey had bad math teachers in high school.
- You're making enough iron, you just didn't account for the timing interference on the belt.
-  I minimize belt usage ever since I was laying belts on a fresh mining world and sucked five planets of iron dry before I finished, thereby shutting off my entire galactic economy.
- ******** - I frequently ack people that if they ever acutally use more than five gas giants, come back and tell me how and why you did that.
- ******** - Scaling up never ends, yellow is just the first time you're forced to choose between scaling up or waiting three weeks.
- ********- the slider system on the utility slots in the personal inventory can be complicated
- I still don't even use the bots that way
- awwww it's a love mop
- he just spent the last ninety minutes experimenting with fractionators in sandbox, but now back to the run
- it's around twice the footprint and complexity of belt method peak performance, which means I won't be using it... but with some ultra nitpicky refinement of the concept I'll see what I can do about compression.
- right? That feeling is amazing.
- I'm thinking maybe chains of three fractionators packed ultra-tight together every five boxes could result in coming close to the cross-peaks of all efficiency demands.
- that's only a thing at certain longitudes
- Once I finish the mall at my O type I'll have everything I need for planet scale experiments.
- it's always been a thing
- when you drop something it's like a windows error
- eight stacks rings of fluid storage at the pole of an icy world at the edge of my starter system... fourteen rings deep around a group of ILSs directly on the pole pulling in 90 vessels worth of hydrogen at a time
- none of it was orbiutal harvested, that entire planet's purpose was just to pull in the waste hydrogen from everything else. It produced around 20k deuterium per minute towards rocket production at peak output.
- not tricky, just logi bots
- you can pack six boxes around one assembler, and you can pack six assemblers around one box, or eight smelters around one box... and this while still having the possibility of output belts. I want to do some tiling pattern experiments with each of the recipes.
- I did titanium glass that way on the last run before my hiatus, and it worked amazingly well.
- yeah it looks like the planet is smiling and has dark glasses on, but it's all box line smile and PLS with a two way nonstop flow of drones to a huge planet-wide ring of coil production and a max stack titanium smelting line down the meridian and a line of PLSs with distribution points and another with collection points... yes. There's an image. LUL
- you can see hexagon patterns emerge in the matrix of production
- yeah, yellow tech sucks for box method. Belts are actually a little faster in yellow. But one upgrade cycle and you're 3x faster... then another upgrade cycle and you're 18 times faster, and then another upgrade cycle and you're 50x faster
- I've never really invested time and resources into expanding the sphere at the starter star like I've been doing on my current run, and there has always been a game-breaking update before I can start mass producing rockets for the scoreboard... so I have ended up doing more science per minute on this run than I ever have before.