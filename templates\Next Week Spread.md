<%*
  // get the Date object for next Monday
  function getNextMonday(from = new Date()) {
    const day = from.getDay()
    const delta = ((8 - day) % 7) || 7
    from.setDate(from.getDate() + delta)
    return from
  }

  const weekdayNames = ["<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>"]
  // start on next Monday
  const baseMonday = getNextMonday(new Date())
  // build an array of the seven days
  const week = []
  for (let i = 0; i < 7; i++) {
    const d = new Date(baseMonday)
    d.setDate(baseMonday.getDate() + i)
    week.push(d)
  }
  // output in descending order: Sun → Mon
  for (let idx = 6; idx >= 0; idx--) {
    const d = week[idx]
    const dateStr = moment(d).format("YYYYMMDD")
    tR += `## ${dateStr} ${weekdayNames[idx]}\n-  \n`
  }
%>

