---
date: 20230222
areas: DysonSphereProgram
title: 
description: 
updated: 
category: dysonsphereprogram
---
# Summary:
- 202411290805:  are a list of tips and tricks in the game [[DSP - 00]]. These are usually "tactical" in nature and serve as a easier, shorter, more fun, or "cheaty" way to complete relatively small tasks.

# Ideas



## Video Ideas
- 202502260804 - [Twisted Belt Tutorial] Not many people don't know about how this works (even me) including how twisted belts work with sorters.
- 202412050936 - [Belt Labels for Any Item in Blueprints] Any item will be displayed even if item is not researched. This is helpful for making blueprints that could be a bit too advanced in the early game.
- 202411300752 - [BAB As Building Field Repair/Replace/Scavenger] - This is a somewhat higher level tip, but with some setup a BAB can receive buildings and automatically repair buildings nearby. On the other end, BAB's can "filter" fogger drops and leave out items that are not needed.
- ************ - [Belt Elevators are Even Easier to Make Now]
- ************ - [Hot Keys and Buttons are Tips Also] - As I said in ************, tips and tricks are things that makes things easier. This includes learning hot keys. The ones that are really good (I mean to share with everyone) are pretty obscure or hard to find in the guide book. They devs are doing an amazing job showing the hot key tooltips for various contexts.
- ************ - [Can See Many Shortcuts and Hotkeys In The Settings Menu] - I certainly encourage you to check out the settings menu in the game. There are whole list of "actions" and things that can be assigned a hotkey, if the aren't already. Explore them once in a while, if there is something you don't understand...call me...I'll be your wingman
- ************ - [Order Blueprints by Numbering the Filename] If you want to order blueprints in your faboriote order, just rename the main filename to contain number that is easy for you label. The game will order then in aplphnumberic order
## 


# Notes
- Search Terms: Dyson Sphere Program Tips, Dyson Sphere Program Tips and Tricks


- [Switch Between God Mode View and Normal Mode View] - They both have their pros and cons but God Mode makes it easier to build from far while normal view makes it easier to perform up close and personal edits to a build. It's strange that there isn't a toggle button for the the different views in vanilla yet...[Uncomfortable Game Shorts](https://youtu.be/oD2-bpsHDOQ&t=10)





## Archive Tips
- BAB Mall (obi-daddykenobi2031)
- How to use Super Nova Effectively
- Prioritize construction and repair
- Fleet replenishment
- Take out the fog warning sound...
- Mark belts with more items (SalvaBarbus)
- Armor discount (Vladimir S)
- you can rotate pumps now...
- If it doesn't work...try again...with blueprints.
- Belt elevators are REAL, Easy belt elevator
- mass copy/paste building setting with blueprint mode
- Please spray the spray
- insta build in sandbox mode
- Baziwan9407: you can throw explosives to the ground buildings from orbit!
- A BAB range can be extended by signal towers?! SeniorPolution630







## Experiments
- 202502171457 - [Does Bots Ignore Distance Limit When Transferring to Mech?] Along with bulding mall items, this can be a very helpful say to get by without a mall in max fogger setting (just make sure to test this first) (answer: they don't. They will only deploy exactly when in range of the mech)











## Discussions
- 20240311 - [Dyson Sphere Program Advanced Gameplay Tips I Wish I Knew UGS](https://www.youtube.com/watch?v=fIGdzGRc4pU)
- 20221018 - [New Advanced TIPS & TRICKS To Improve Your Game | Dyson Sphere Program | Tutorial / Guide](https://www.youtube.com/watch?v=pX3aa5MmngY)
- 20210615 - [How To Start Dyson Sphere Program Properly (Beginner Tips)](https://www.youtube.com/watch?v=yZHim3zwvYk)
- 20230222 - [NO WAY YOU KNEW THIS! | DYSON SPHERE PROGRAM | TIPS AND TRICKS](https://www.youtube.com/watch?v=e9K8AyKPxmw)
- 20220114 - [TOP 20 tips in Dyson Sphere program](https://www.youtube.com/watch?v=e6DWf4tcxSw)
- 20210129 - [DYSON SPHERE PROGRAM ►10 NOT Blindingly Obvious Tips  ► New Factory Simulation Strategy Game 2021](https://www.youtube.com/watch?v=Bym9CU5UbJA)
- 20210128 - [Top 15 Dyson Sphere Tips and Tricks 🤖 Tips for Beginners and Veterans 🤖 Tutorial, New Player Guide](https://www.youtube.com/watch?v=PeyuGpeIxNg)
- 20210124 - [DYSON SPHERE PROGRAM - Tips & Tricks for beginners.](https://www.youtube.com/watch?v=gNkvh6XPw3k)

## Log
- ************: Revised note a little bit, added twisted belt and twisted sorter tips!
- ************: I'm considering my first tips video for the gameIf I collect up to 10 tips that I think are worth sharing, then I will do so. Any more than that will feel to burdensome and any less than that will feel cumbersome (to publish) as well.


When constructing layouts…I mean like really, really large stuff…

It’s fairly easy to leave behind stuff unbuilt. There’s always some belt segment or batch of sorters out there…somewhere

That’s where the construction alarm becomes a gift, when used right. Let me explain…It works like this…

When you slam down a blueprint. It will consume all the available buildings from the main inventory
 
Those become the green buildings…

Any buildings that remain to be consumed

Those are the “white” buildings…

are put in the construction alarm.

Now the construction alarm is nifty because it will…quite literally…point at where those white buildings are located.

It even works for missing buildings on another planet. Though I don’t recommend letting it go that far. Believe me that can get annoying.

Stay with me here…that cool locator? It works for the white ghost building but it doesn’t work for the green ghost buildings therefore no location. 

So…next time you slam down a very large blueprint…try it with an empty with an empty inventory. 

Therefore the location of all the buildings lights up.…

and yes…they stay up even after you pick up all the needed items.

# Relationships
- 202409261944: [DSP - Place Miner Then Bury] is a  where viens can be buried after [Miner] placement and they will still be harvested. This is usefull to do when tightly placing buildings next to working miner. (You want to place a building but there is a vein in the way? Well that’s what burning veins is for, right? Oh no, if I’m using the vein right now? What do? Well it turns out, if you bury a vein that is already tapped by a miner. It will still work. Nice.)



- Try to find the star with the highest luminosity will produce the highest energy
- Next leve, find a star with planets "inside radius" [Stars That Swallow Planets]

```vid
https://youtu.be/e9K8AyKPxmw?si=ezyoES3c74bI7GhG
Title: NO WAY YOU KNEW THIS! | DYSON SPHERE PROGRAM | TIPS AND TRICKS
Author: Soros001
Thumbnail: https://i.ytimg.com/vi/e9K8AyKPxmw/mqdefault.jpg
AuthorUrl: https://www.youtube.com/@soros001
```



