---
date: 20230825
areas: DysonSphereProgram
title: 
description: 
updated: 
---
Now I’m here developing a circuit board production yet something is clearly wrong here…

The factories are not working…what kind of alarm are we getting…

 product stacked? Well isn’t that weird.

The mk2 sorters here can output 180/min…

And how much is the factory making…let’s click on it and…

The easiest way know how much a building is making just click on the building! Hopefully…the factory is powered and working, and it doesn’t matter what kind of proliferator or tier of factory it is…

and you can see the production rate and all of that…and here I see it’s making 180/min. So I should have just the right sorter right? right?

Well no…not really…and that brings me to my next tip…(more like a anti-tip…or something?)

What the factory is actually reporting is the cycles/min, not the actual output. The factory is basically following a cycle where it eats some inputs, goes through a cycle, and then spits out the products…all according to the recipe.

And sometimes, would eat or spit out…more than 1 item. In this case it spits out 2 circuit boards per cycle. So basically I need a speedier sorter. There we go.

So…many people have fallen for this…even me…but not you…your welcome.
