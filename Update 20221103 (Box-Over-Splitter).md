---
date: ************
areas: DysonSphereProgram
title: 
description: 
updated: 
---
# Relationships
- ************:  is a [[Game Updates]] featuring the ability to place storage boxes over splitter

# Ideas

# Notes
Folder: ************
Video Title: Dyson Sphere Program | Update | Box-Over-Splitter | +Viens Info | FPS Quality-of-Life

## Script
Hello viewers, you’re watching Dyson Sphere Program where here you can find everything DSP related including information, news, build guides, strategy, and theory crafting.

On November 22 YouthCat released the next early access patch to Dyson Sphere Program with the following additions:

The first change is that mk1 storage boxes can be placed on top of a splitter. Basically items going into the splitter will be routed into the box above, and vice versa. 

Yes, you can stack boxes on top of each other to expand the storage space. And yes logistics distributors can be placed on top of boxes as well. No you cannot place a splitter on top of a box.

I’m guessing the intention here is an easier alternative to loading and unloading boxes to conveyor belts without using sorters. However, one glaring drawback is that the splitter output doesn’t pile.

As an input though, there might be slight edge for the splitter-box combo because they do not need be configured to input a specific item.

I should also add in here that belts can now run over storage boxes. I was not aware that you could not do that but now that I am aware I’ll be thinking of ways to put that mechanic to use.

in planetary view we can see the next change that’s done to the ore information. On the right side viens information, there is now a drop down with three ways to view resource amounts. The first is planet reserves which shows total amount of the resources on the planet.

Mining planned which counts all the ore that is currently tapped and running. And “not planned yet” which is all the ore that is not been tapped.

Resource “rates” that is, resources that are expressed in per min seem to account for veins utilization level. Also by clicking on the systems star you can also view under the same options, for that entire system.

I’m pretty excited about this one. For example, I can go to a planet to mineout. View under “mining not planned” and it will act as an active checklist for me if I miss out on any ore. 

Also I checked infinite resource mode. The resources are still displayed as number of viens. So no mining rates at this time.

The next change is specific warning icons for litter items, so you can find a your “needle in a haystack” easier. It’s nice that litter has an label but I still felt it was hard to find that “needle in a haystack” maybe I’m missing something here.

Then there’s disable metadata so you don’t accidentally press the wrong button when browsing research.

Chemical plants now have a working animation. 

And the last final two change center around performance optimization. If you press shift and f12 on the top left will be shown a frames per second / updates per second meter. Normally this is not shown unless you start to have issues with fsp/ups. The change here is there is now there is a settings icon next to the meter which reveals two bars. Green for the frames per second and blue for the updates per second.

Mousing over the bars will go to a detailed explanation of how these two things work together. For now just think of it as bullet time. When the frame rate seems low and jittery, then increase the green bar. The fps -should- increase but you will notice everything else seemingly slowed, that’s the game slowing down game-time just so you have an easier perceived frame rate.

Depending on your hardware and size of your base this option can definitely be a life saver but may take some getting used to. In my own megabase I am noticing some improvement using this option albeit there is some noticeable tearing. In addition in low fps, there was this annoying bounce that icarus does when jumping off to flight mode. Which bring us to the final change. it looks like that annoyance has been fixed, yes.

And of course several bug fixes which if you know, well you know.

All in all not a bad patch. Nice changes all across the board from build systems, mechanics to quality-of-life. Hope you’ve enjoyed the video and found yourself informed. See you next time.