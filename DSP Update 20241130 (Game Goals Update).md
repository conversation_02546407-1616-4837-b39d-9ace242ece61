---
date: 202411300810
areas: DysonSphereProgram
title: 
description: 
updated: 
---
# Relationships
- 202411300811:  is a [[Game Updates]] and [[DSP Video Projects]]

# Ideas
- 202411300814: [Voice Broadcast] There is a second voice added to the broadcast system. I don't know the name of the voice but seems to mostly be toward female English voice
- 202411300813: [Statistics Panel]
- 202411300812: [Blue Print Optimizations]
- 202411300811: [Game Goals System]



# Notes
Folder: 202411301045 DSP Update (20241130)
Video Title: Dyson Sphere Program | Update | Game Goals | Stats Panel QoL | Blueprint Mode QoL

## Log
- 202501021225: I published the video to YouTube. I'll monitor my email for any comment traffic (not expecting any) but after a week in the open I'll debrief myself on how I did with the whole thing. 


## <PERSON>rrata
- Missed mentioning the "buffer change" to stacks of matrix labs. It's kinda interesting but I don't have the whole story about that. Long ago, they took FOREVER to fill so the buffer was decreased drastically to fill them faster (so I though). Now the buffer is back but perhaps the transfer speed between the "floors" make up for it?

## <PERSON><PERSON><PERSON>

OK, You've been playing Dyson Sphere Program for a while.

And for whatever reason, you decide to take a hiatus. 

Three years later you go back to one of your old games

and you are wondering to yourself

...what was I doing in 2021?

Well...wouldn't it be nice to have some sort of checklist to nag yourself and procrastinate on?

Hello programmers and welcome to Dyson Sphere Program 

1.6 million meatballs served!

And YouthCat has once again blessed us with their next regular early access update. 

And this time I'm here to talk about it.

Sorry Station Update you were pretty good too...but there was a HUGE amount of background to cover. And just didn't have the tim

On that note, I would like to apologize for not going through the station update AND the logistics panel updates those were really good changes to logistics

So much so I might make a proper about it...

Anyway, this is just a regular update here 

and I don't know if you know

next major update they are working on putting in planes, trains, and automobiles

Actually one of them is probably not coming...but I can dream right?

mmm...space trains...

wait! That's no moon...it's a space station...

But For now this update they have added a few cool things

some of which in my opinion will pay off big in the long run...

let's go check some of them out.

First is the  [GAME GOALS] System. 

Or should we just call it "Frequent Hints"? 

Either way you now have the option of having a todo list on the side of your screen.

Some of us has played with the concept for a while now. 

But leave to the Devs to make it look good.

As for the content of the list

from what I've seen, it's basically milestones

I'll admit it was fun at the start being shown what I need 

10 logs, research some tech, make a building. o

Instant satisfaction.

But then eventually you are being told to eject 10,000 solar sails? 

Or build a Dyson Sphere in a 2 or more luminosity star? 

That's a bit much to plan your 1 hour nights around don't you think?

And for us choosing the explore option? 

Well you can can set your own goals...in notepad!

Yeah seriously I tried looking around and for the life of me, I don't see a way to type in my own "goals" yet. 

I was expecting a simple in-game todo list, but for now I guess us explorers will just stick with notepad. 

By the way free-tier tick tick is not bad

anyways I'll have to go through the all of the game hints to know for sure

but I'm looking forward to seeing some improvements in system especially if multiplayer becomes ever becomes a thing

Just like the game goals,  [PRODUCTION PANEL]  is used to quickly assess what's going on, what problems there are, and basically what to do. 

and in this update it's gone completely bonkers with improvements.

For starters the number of grouped favorites have increased from 3 stars to 6 stars... 

Wait a second

While 6 is indeed better than 3, 

I might as well mention why I wasn't using the "star favorites" in the first place

I don't remember what the heck I even made 1 for

or 2, or 3

You think 6 is going make it better?

In my opinion I think it can be very powerful to let us name our own favorite group

besides a number

We can have named groups like Cubes, Ammo, Fuel, Mall Buildings

Sky's the limit...no wait...

Now this is exciting 

an actual [SEARCH ITEMS] bar to quickly get to that item you are curious about. 

No more scrolling around aimlessly...

Though you will have to remember the use the actual item names, 

blue motor or purple cube is not going to cut it here.

You can also bring up items by so called upper and lower stream production 

so in a way you can see all the items that your resources being sent to

or all the items used to make a particular item, 

so you can find that bottleneck. 

Speaking of bottlenecks, a really addition to the production panel called the [REFERENCE RATE] 

and this number here is basically a theoretical production 

or consumption of an item based on all the factories in the field

Heck, it even accounts for proliferator bonuses

What's a proliferator? 

sigh...another video perhaps...

basically if your production reference rate is less than your consumption reference, you probably

Moving on...

The right hand of the production panel has been revamped as well, 

focusing on the interstellar movement of items.

If you haven't noticed I've gotten rusty on my interstellar logistics...

but from what I'm seeing this section is just tracking items tucked away

notably stations and storages

Interesting that litter is included as well, this includes fogger drops 

I suppose if quantum chips are dropped and it's not being picked I guess I can tell now

Moving on from production...more stats are abound!

The [Statistics Panel - Power] has changed for the better

All of the generators and consumers of power are now divided by each of the building types for our viewing pleasure.

There nothing much to say here

but I would like to add here that renewables are still not being used right to save on fuel???

If you know what I mean and think I'm wrong, or have a possible workaround please comment

Now a better panel to look at is that [Dyson Sphere Stats Panel]

As a quick review...spheres are made up of structure points and cell points.

Solar sails are launched into orbit 

and are consumed as cell points toward the sphere

And Small Carrier Rockets are launched as well 

they go straight into the sphere as structure points

in the bottom right corner is the entire progress of the sphere

From currently built to whatever amount is remaining.

You thought that was here? 

ahahaha we're just getting started...

Let's go to an actual sphere being made...

And there it is...in all it's glory...

I've seen better

On the left we now have a radial chart that basically summarizes the sphere progress. 

But for each layer now

And now layer specific stats are shown in the lower left corner.

And best of all. We now ACTUALLY know how much power a sphere is eventually going to make

No more nerdy spreadsheets

Even the max solar sail absorption rate is shown now

It's these things here the sails turn blue and make a nice little path to this hole. These holes are called nodes

And the more nodes there are, the higher the rate of sails that can be consumed into cell points

Which brings us to the next change

before the base rate of these sail getting eaten was about 30 per minute per node, 

but this update the devs have nerfed it down to 10 per minute per node

sigh...and then turned it into it tech that can be upgraded to 360 per minute per node!

Now we can see sphere constucted in our lifetimes!

Ok now we're done with stats

Next is the changes in [BLUEPRINT MODE] and are mostly visual.

Now, that's not saying a lot...so...uh...let me show you...

So the contrast between the planet and the buildings are much much better.

Notice that colors of the building pop out more, yellow and green. 

There is a slight cost to this, the planet itself is a bit darker 

but hey it's not that bad, 

and have you seen how much the resource nodes pop now? 

This is amazing. 

And there are now two hotkeys added. 

These are held down when you are having an invalid placement.

The shift key reveals elements that made it through

While the ctrl key reveals elements that did make it through

So it'll be easier to see that one spot you forgot to put foundation over

And when placing a blueprint the color of the elements can be changed to 3 different colors, 

so similar elements can be distinguished

In the sound department, the game introduces a new voice for [VOICE BROADCAST] 

its female, more english, 

as before the events the broadcasts happen can be configured.

There are also some [BUILDING SOUNDS] were added as well. 

Namely the Quantum Chemical Plant

The Negentropy Smelter

Advanced Mining Machine

Satellite Substation is the pretty quiet

As is Geothermal Power Station

Personally I liked signal tower the most, it gave the most eiry sci fi sound

The [POWER RANGE] of power buildings is a very interesting change

Before even the satellite substations couldn't reach high enough

but now even a tesla tower can reach all the way to the very top!

Though the only buildings that I can think that could need power up there is sorters.

Look, I'm not saying what you can do up there. I'm just saying it's possible now.

And speaking of building in the sky [CONVEYOUR BELTS] are now MUCH easier to build in the sky. 

All you have to to do is stare at the ground. 

Much easier to build complicated belts high from the ground.

More changes of note include [SHIELD CONTOUR] 

They are shown now, so you can see if your are missing a spot during space attacks. 

And the space attacks and [SHIELD HIT EFFECT] were changed

not that I could notice. 

[Construction Drones] look cooler now. They don't look like that guide bot from Descent 2 anymore

So, there you have it—a quick dip into the fresh updates that YouthCat’s cooked up for us. 

Sure, we might be waiting on notepads becoming in-game goals 

or the dream of steering your own train...I mean spaceship

space trains...

but this update is all about making your cosmic construction journey smoother, 

smarter, 

and a lot more satisfying. 

Looking forward to the next update and see you in the next build.






