---
date: 202403100307
areas: DysonSphereProgram
title: 
description: 
updated: 
---
# Relationships
-  is a [[Problem]] where there is a need to significantly alter an already established factory for the purpose of improvement or other reasons.

# Ideas
- 202502200632: [Refactoring Can Extend All the Way to Restarting the Game] In a refactor, the entire "base" is redone to a improved paradigm. If the time and effort it takes to finish such refactor is comparable to just starting from scratch, it makes sense to restart the entire game.

- 202502200634: [Finding Perfection and Happiness in a Factory] I believe that part of the factory game is seeing that there are improvements to do, and finding ways to do them. Nothing is ever perfect, which can be a beautiful thing to realize...

# Notes
## Discussions
- 202503041022 - [Am I cooked?](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1j3d63j/am_i_cooked/) - go to another planet, use PLS/ILS, nice story about how software is "scaled up"
- 202502191802 - [When should I take the plunge and rework my chaos?](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1itjexo/when_should_i_take_the_plunge_and_rework_my_chaos/) - Sometimes just building on a new planet is enough to satisfy that refactoring itch




# Conclusions
