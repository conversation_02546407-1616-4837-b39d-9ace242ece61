---
date: 202411061124
areas: DysonSphereProgram
title: 
description: 
updated: 
---
# Relationships
- is an [[Item]]
- 202411062325:  is a checklist of blueprints toward making universe matrix

# Ideas
- 202411070736: [What Has Happened Since Feb] There are many things that has been changed in the game. Nothing major, just a bit more belt sorcery for the masses, a second thing that I don't know about, and an actual logistics panel. The dev also mentioned putting in vehi...wait what? A logistics panel? Now this we have to see...




# Notes
## FactorioLab
- [Universe Matrix 360m](https://factoriolab.github.io/dsp/list?o=universe-matrix*360&v=11)
- [Universe Matrix 360m](https://factoriolab.github.io/dsp/list?z=eJwtyLEKwjAUQNG.yXCnFrHbG3wP0kWLgoJOIlhESimkVbRDv11MMh04g-jMqipcK9QuiB8YKVwQfRL1E-OfOWJNWttmd9lNckqsE.rI3qLtR.ZcuNPx4kuFNugJPaMd5herscNiR9f3Qa7uLWX5Axp-KnA_&v=11)
- [Universe Matrix 360m (Some Spray)](https://factoriolab.github.io/dsp/list?o=universe-matrix*360&e=*proliferator-3-products&r=universe-matrix**0&r=gravity-matrix**0&r=graviton-lens**0&r=information-matrix**0&r=particle-broadband**0&v=11)
- [Universe Matrix 360m (Fire Ice and Some More Spray)](https://factoriolab.github.io/dsp/list?z=eJwtxbEKgCAUBdC.ecOdlMjtDT0hN2kpak8aDAKpkAa.PcjOcg6WB41RFBiOEtsOUJT4rOlaW5Ppf6tb.x0yD1iwIuJChoF4yAiZIbHIDtsX6-hmrV8CRRyF&v=11)
## Reddit
- ************ - [Research consumption massive jump? Bug?](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1hf5vul/research_consumption_massive_jump_bug/)




## Slip Notes
- The game is a bit a more complex than vanilla factorio, but it's certainly no pyanodon
- I mean...I'm really setting the bar REAL low here. If I can beat this...then so can you!


## Log
- ************: CogNAN_  is buiding up a 100/s White Cube production in one planet. He post his math [here](https://factoriolab.github.io/dsp/list?o=universe-matrix*100&odr=0&ist=4&rex=P*Y~Z*c*k*u*z*6*BN*BU*BX*Bl*CG&mpr=2&mfr=hydrogen&mps=proliferator-3-products&v=11)
- ************: Clearly this is hard to read the CSV downloaded version...
- ************: made the first version of https://faxctoriolab.github.io/dsp/list?o=universe-matrix*60&v=11 it will be changed I think but the first thing to do is to make the iron ingots! https://factoriolab.github.io/dsp/list?o=universe-matrix*60&v=11





