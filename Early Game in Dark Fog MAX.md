---
date: 202312190942
areas: DysonSphereProgram
title: 
description: 
updated: 
---
# Relationships
- 202312190942:  is a [[DSP Situation]] where the player is only starting research but [Dark Fog] due to max difficulty has already begun sending waves of units to the enemy.

# Ideas

# Notes

## YouTube


```vid
https://youtu.be/IKiTNLqzi_A?si=aPebkkJBJc2ojCP6
Title: Dyson Sphere Program | Dark Fog | Rampage Hardcore Mode | Let's Play - Ep. 2 | Ammo and Power
Author: Phantom2502
Thumbnail: https://i.ytimg.com/vi/IKiTNLqzi_A/mqdefault.jpg
AuthorUrl: https://www.youtube.com/@Phantom2502
```
















## Script
- I've decided my next playthrough of Dyson Sphere Program will be in max difficulty
- That means at the start of new game, click on the Dark Fog Settings and just crank every setting to the right.
- Very difficult? I'll let you judge, but fun? After trying it a few hours, yeah I think so.
- Hi I'm <PERSON><PERSON><PERSON> and Here I'm going to show a glimpse of what MAX difficulty is like and in my opinion the surefire way of getting through the first few waves without any metadata whatsoever...so you new players can actually give this a try.
- Anyways, here you are now, you just landed. 
- Don't save the spaceship this time. Might as well use the fuel and stuff
- Typically you would setup miners, smelters, and assemblers, to begin research and building automation...
- We're not going to do that here. The threat level increases too much and you are attacked before you can get your first turret down.
- The threat level seems to rise with energy consumption not power generation. Basically, if you do nothing, they leave you alone.
- This also means another thing...be efficient.
- Every building made, every component, needs to well...be pretty good.
- Well regardless newbs can get through the first few waves like so...
- Icarus is going to make all the starting stuff by hand. This is way because the mech is much more energy efficient than factories and therefore will greatly minimize threat.
- So make 10 gauss turrets and lay them down. 10 is my preference, 5 is ok, and I have seen some players get away with just 3 but you'd probably need more soon after the first wave.
- For good measure you will want to include some ammo production. Wind turbines are down yet, so the buildings are not consuming any energy and therefore there is ample time to get ready.
- For convenience there's an example blueprint for ammo production link in the description.
- https://www.dysonsphereblueprints.com/blueprints/factory-magnum-ammo-90-min
- While this will only keep 5 turrets constantly firing. There is enough time between waves to make ammo for more than 5 turrets. this a actually a case where you can have a buffer (box or otherwise) and there will be enough ammo made between waves to maintain more than 5 turrets
- You should be ready when threat is about 60%....just lay down 10 to 15 wind turbines and have fun!
- Now the waves are coming but we're ready now.
-  A few tips though, I didn't know this yet but in this update in you can instantly construct buildings one at a time by holding left mouse button the ghost building. This is helpful because starting construction drones are just too damn slow...
- Also press z to go into combat mode. One feature is you can hold down the reticle on mobs and that somewhat increase the Mech's damage rate
- Also there is supernova charging...where the turrets charge up and then fire a quick volley at everything. Instead of clicking each indiiual turret to charge, you can assign them to groups and when one turret is charged the others will follow as shown in the number next the group. That's as far as I currently know. It seems a bit convoluted but every little bit helps.
- With some luck, the mobs are destroyed, you get some drops to work with, and now the race is on.
- Eventfully the production will slow down as well as the waves...but when the waves do come they will become bigger and bigger. What was around 16, becomes 50, then 100, even 180....basically you will have to juggling expanding defense, automatic production, research, and the mech replicator. There's little room for all of them. Did I mention using metadata? Good blueprints? Save scumming?
- Can Dark fog even be fully  eradicated at this difficulty or am I just going for the Peace and Love achievement? Either way I think this is going to be very interesting playthrough and hopefully for you diehards will give it a try as well.
- In either case thanks for watching, and I'll keep you posted.





- As far as I can tell, even if you have the most efficient farm, killing dark fog mobs and distributing the loot, it's still not a net positive for you and will end up expending resources to keep the Dark Fog at bay  
- In my opinion I'm not seeing a path
- So I've gotten this far.They might level up to become even tougher and require even more firepower to defeat, I'm not sure