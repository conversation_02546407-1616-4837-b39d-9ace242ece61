---
date: 202307130829
areas: DysonSphereProgram
title: 
description: 
updated: 
---
ELEGANT WHITE CUBES BUILD
-----------


INTRO
-----
Hello engineers and welcome to dyson sphere program builds! My my it has been a while since my last video but since then I have learned a bit, built stuff, you know...

BUILD
-----
On this video we're going to striaght to end game and settuing up universe matrix herefoward called "white cubes". Every other color cube that has been made so far, mix up some antimatter and you get the glowy sugar cubes. These items are consumed by other matrix labs for research and...that's it. Clearly they are extemely time consuming to set up and most eninggers won't have the patience (for early release anyway) or even the hardware (again cuz early release) to make anything beyond a 1 cube per second.

But when you and vessels can warp across clusters in seconds, release dozens of buildilng drones, and have full belts of resiources coming out a single building, kinda adds to the overal appeal of going beyond

RECIPE
------
As said before, the whites are made in research labs. They input all the colored cubes of the blue, red, yellow, purple, and green varities, and antimatter...and then coughs up a glowing peice of boring furniture. The white cubes are then sent to be burnt off in another lab for research.

TYPICAL
_------
Typically, the lab making the whites is flanked on both sides with 3 belts each. Straight forward and no issue here but how those belts are fed and how the output is handled will be point of this video as that will have a massive effect on copy/paste layouts and scaling

BEFORE
-------
The ways to feed labs with rainbow cubes and antimatter (which is most of the game) would take way long to cover in a few minutes, very short...you can choose to have some or all ingredients in made in planet. You can go to DPS blueprints website and find a few blackbox prints making everything needed in the 1-5 or perhaps even 10 per second range? that are in my opinion not bad, 

The other way is have ingredents made off planet which is the meta yours truely is adopting here. It certinaly feels like more organizing is needed and its no surprise as engineers have been naming their planets after in-game items

With vertical stacking fully researched a tower of 15 labs will make exactly 1 cube per second. Max belts being 30 per second all one needs to do then have exactly 30 said stacks output the maximum single belt. Some engineers at this point would just have a whole line of these labs going through a third of the planet (no problem). I mean you don't have to it's just easier that way

Let's finally talk about output. The whites going straight to resereach right? What somewhat face palming (to me anyway) is where those glowing minimalist chairs are off to...I have seen every engineer offloading the white cube to the side to be belted somewhere to some other lab. THIS IS A MISTAKE. With a direct insert mentality one can see the white cubes can just go striahgt into another matrix lab! Think about it...where eles are they going? Doing this will save one sorter per stack of whites (1/s) which can only get better during scale up


Again making the ingredients won't be covered here, but of importance here is how the inputs are laid out. The ILSs can be divi'ed up however you choose. Here I'm going with one ILS handling blue, red, yellow and the other ILS handling purple, green, and antimatter. If not apparently I'm setting up 4 full belts or 120/second, 7.2k/min (seriously which unit makes most sense is beyond me).











DIRECT INSERTING?
-----------------


