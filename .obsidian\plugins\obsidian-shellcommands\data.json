{"settings_version": "0.23.0", "debug": false, "obsidian_command_palette_prefix": "Execute: ", "preview_variables_in_command_palette": true, "show_autocomplete_menu": true, "working_directory": "D:\\Scrapbook\\202405122132 Python Scripts", "default_shells": {}, "environment_variable_path_augmentations": {}, "show_installation_warnings": true, "error_message_duration": 20, "notification_message_duration": 10, "execution_notification_mode": "disabled", "output_channel_clipboard_also_outputs_to_notification": true, "output_channel_notification_decorates_output": true, "enable_events": true, "approve_modals_by_pressing_enter_key": true, "command_palette": {"re_execute_last_shell_command": {"enabled": true, "prefix": "Re-execute: "}}, "max_visible_lines_in_shell_command_fields": false, "shell_commands": [], "prompts": [], "builtin_variables": {}, "custom_variables": [], "custom_variables_notify_changes_via": {"obsidian_uri": true, "output_assignment": true}, "custom_shells": [], "output_wrappers": []}