---
areas: DysonSphereProgram
date: '202307131116'
title: 
description: 
updated: 
---
Over the weekend I finished drawing a 9 layer sphere based on <PERSON><PERSON><PERSON>'s 576 node design. To overlap the nodes/frames as easily as possible I unchecked Run game and created all the layers. Starting at the equator, I placed a single line of nodes/frames starting at shortest radius (away from me) and repeated for the next line at the next radius (toward me). Repeat the lines going toward each of the poles. Once all lines are placed I repeat the whole thing for the diagonal direction.

I ended up with ~338 nodes per layer and 5529340 total structure points, so at 2.505L it should yield 531 GW. I am probably messing up somewhere. I need to find a way to check the number of frames. Also, this took 6-8 hours to finish. 

Sadly, the frames don't look this organized any more. The layers seem to have slightly different rotational speeds so all the nodes have drifted apart after only a few minutes.

edit: oops, I even said it was 2.505L and yet forgot to multiply that with the 531 GW. The power should actually be 1.3 TW!
edit: Also this design is BAD for including shells, no one in vanilla (without mods) will be willing to spend ANOTHER 8 hours (probably more) clicking all the shells, and this is assuming the layers were never scrambled up.