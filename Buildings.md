---
date: 202412151208
areas:
  - DysonSphereProgram
title: Building
description: 
updated: "202412151209"
---
# Relationships
- placeable assets in [[Dyson Sphere Program]]
# Backlinks
```dataviewjs
// Get all incoming links except a self‐link
const backlinks = dv.current()?.file.inlinks.filter(b => b.path !== dv.current().file.path);

if (!backlinks) {
  dv.paragraph("Error: Unable to access current file.");
} else if (backlinks.length === 0) {
  dv.paragraph("No backlinks found.");
} else {
  dv.list(
    backlinks.map(b => {
      // Load the page object for this backlink
      const page = dv.page(b.path);
      // Look for a field 'description' (frontmatter or inline)
      const desc = page?.description ?? page?.file?.frontmatter?.description ?? "";
      // Build the line: [[path]]: description (omit colon if no desc)
      return `[[${b.path}]]${desc ? ` - ${desc}` : ""}`;
    })
  );
}
```

# Notes


## Log



