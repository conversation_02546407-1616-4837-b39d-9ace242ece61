---
areas: DysonSphereProgram
date: '202307091046'
title: 
description: 
updated: 
---
# Relationships
- is an [[Item]]


Below is a apprarent calculation on what happens to the rest of the "production" when one of the steps is producing less than what is needed

I think the conclusion here is that the higher level production is effected but no more than the percetage of that particular level. Example if processor is produced at 99% intented, then all productions depending on that will also be down by 99% as well. But only linearly it seems...




Processor 100.012/min
White Cube 55.810/min

Processor 99.904/min
White Cube 55.750/min

Processor 99.89%
White Cube 99.89%