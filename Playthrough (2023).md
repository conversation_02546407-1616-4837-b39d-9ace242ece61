---
date: 202306290544
areas: DysonSphereProgram
title: 
description: 
updated: 
---
Episode 1

Hello, engineers. It’s good to be back with you with another video. Still playing Dyson Sphere Program! As of this writing, DSP is still in early access, and according to YouthCat’s last announcement…they are currently giving full steam ahead to incorporating combat.

That means that very likely the next update, you will scale up, maintain, and at your choosing, defend, your factory. No doubt, this will only add to the factory building complexity.

On this video, I’m starting a new seed. With the intention of building up a large megabase from scratch in front of you, my youtube builders.

In general I typically don’t go through gameplays…I feel as though I am presenting everything and nothing at the same time.








That thing there is a Dyson Sphere.


Until that time what I will be trying to do is present to you my play through. My intentions for these kinds of playthrough videos are to as big a megabase from scratch as I can and present to you how I go about doing it. Take that as you will.

So let’s just go ahead and get started.

The seed I’m using has two particular features that I’m looking for:
Smaller spacing between stars than usual
Higher availability of unipolar magnets…that’s a raw










The following is a list of researches to go through in order of importance. 

Research que can only go up to 7 so the tree may need to be visited more than once

PART1 (bootstrap)


Research
Electromagnetism (wind turbine, tesla tower, miner)
Automatic metallurgy (arc smelter, glass)
Basic logistics system (belt mk1, sorter mk1, storage mk1)
Basic assembling process (assembler)
Mecha Core I
Drive engine I
Electromagnetic matrix (blue cubes)
High-efficiency plasma control (wireless power tower, pr)
Universe exploration
Communication control I
Drone engine I

PART 2 (one small step…)
6 smelters
Assembler 5 buildings
Research
Blueprints?
Improved logistics system (splitter, sorter mk2, traffic monitor)
Smelting purification (graphite)
Steel smelting (steel)
Fluid storage encapsulation (storage tank, pump)
Plasma extract refining (oil extractor/refinery)
Energy matrix (red cubes)
Mech core II
Drive engine II



PART 3 (welcome to the planet)
Titanium/Silicon smelting

Other (gotta go somewhere…)
Electromagnetic drive (electric motor)
Distribution logistics engine
Environmental modification

PART 3 (2nd and last step on 2nd planet…
Hydrogen fuel rod
Thruster
Reinforced thruster
Vertical construction
Planetary logistics system
High-strength titanium alloy
Applied Superconductor



Yellow cubes
Polymer chemical engineering
High-strength crystal
Structure matrix