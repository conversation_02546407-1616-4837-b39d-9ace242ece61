---
areas: DysonSphereProgram
date: '202305252324'
title: 
description: 
updated: 
---
- Players seem to like to put them as garbage collectors, with boxes for each item.


It could be forge worlds, make everything mall, 10k universe matrices per minute, quantum chips, you name it.

They are all similar in that…there will be hundreds, if not THOUSANDS of buildings to put down (now of course that includes belts, they are fair game)

So the question becomes where can logistics bots fit in here?

Well, one thing about building entire planets is everything you brought with you on your maxed out inventory, it’s not going to be enough. In fact I think that’s the hallmark feature of a planet scale build.

So hopefully, player realize what they can do is setup a few logistics stations on the target planet. Set it to demand buildings and take as needed. Fortunately the stations can be accessed anywhere on the planet using planet view.

So, you are still going to run out of items, it’s always belts, assemblers, or something you just forgot to load up. Just go press that m key go to planet view and get those items right? What what go wrong.

Thing is you do it often enough in a session, and it can get very annoying very quickly. And I’m usually I’m using mods to have the station info always-on display (thank you Appuns) and I’m still annoyed. Imagine not having that kind of quality of life…clicking multiple stations looking for iems like your lost laundry.

Well what can we do? Well that’s where the september 2022 update comes in. Logistics distributors and bots are now in with the ability to directly load or unload items into icarus. 

Now I can only say at this point they are ok right as you start out and they are available to you. It’s a decent way to alleviate some parts of a typical game that I would only describe as getting poked with red hot tongs.

But when these came out I was thinking great, personal logistics. Now I can have my planet scale game without pressing the m key all time! Or is it?

In this video we’re going to find out just how far they can go.

It just so happens we have a processor planet to improve. It doesn’t matter what how much its making or how much more. We’re going to scrap everything, and rebuild with the new layouts hopefully without pressing the m key ONCE.

-