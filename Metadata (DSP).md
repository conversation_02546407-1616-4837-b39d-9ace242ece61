---
date: 202204282321
areas: DysonSphereProgram
title: 
description: 
updated: 
---
# Relationships
- 202204282321:  is a [[DSP Mechanic]] where maintained production of [Matrix] is used as a currency toward purchasing "early levels" of a technology

# Ideas
- 202412140436: [Use Metadata to Get Technologies that are Annoying to Not Have Early Game] while most of the time metadata is useless to use since most technologies will be "gotten anyway throughout the game". There are technologies that are worth having as early as possible. One technology I am always thinking of is [Logistics Bot Range] which for some strange reason is limited when starting out. When maxed they can reach anywhere in the planet and getting to this early will result in MUCH less range headache.

- 202412140441: [Metadata is Gained Based on Hourly Production, Not Per Minute] Metadata that a certain cluster has made is awarded based per hour amount. I'm currently wondering if the metadata is lost if the game detects that nothing has been made in the past hour, or it simply awards based on the best hour the cluster has had? (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> says it highest per cluster 202412131232)

# Notes
## Reddit
- 202412131232 - [Metadata](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1hdhh8y/metadata/)
- 202406162323 - [Meta-Structure amount not increasing?](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1dhomfi/metastructure_amount_not_increasing/)

## YouTube
- 20220428 - [What is META DATA & How NOT to waste it! - New Patch 0.9.25 | Dyson Sphere Program](https://www.youtube.com/watch?v=E8AEpEKAuDk)