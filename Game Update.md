---
areas: DysonSphereProgram
date: '202301220400'
title: 
description: 
updated: 
---
LOL
Hello and welcome to another episode of Dyson Sphere Program, brought to you by yours truely.

Today we’re going over an update released by YouthCat studio on January 5th 2022. The update included two quality of life improvements and some pretty cool art which we will explore here.



Automatic Feature in Conveyor Belt Construction: Auto Bridge Shape

Before, the game just told you you can’t make belts slope that low or that high. As of this update, its easier to make tighter bridges. Let’s see what it can do now.

3 tile bridge…ok check…now a standard blueprint 3-tile bridge it is not as tight but they are pretty darn close…the 4-tile bridges are completely identical. 5 and longer…well it becomes apparent how convenient it is to have these bridges come out quickly compared to using blueprints.

Also some “custom” half height bridges appear to simple to make as well. Again, no need for blueprints or tedious belt bending. Awesome. Seems this is going to be great time saver for everyone.

Now that I think about it let’s look at how the game handles off grid bridges. Note that off grid belt pathing is done by hitting the ‘r’ key to switch to that particular mode. Basically the belt pathing just ignores planet curves and goes straight from point a to point b.

Absolutely, wow.

Bridges are decent tight AND off grid? Can you do that with blueprinting and belt bending? Not that easy! Believe me, I tried.

Invert Conveyor Belt Transfer

Now the belt “direction” can now be switched by pressing the “flip whole path” on the belt menu. Fairly straightforward, click the belt, click the button 

This is also handy when using blueprinting to build fairly large layouts. There

Let me show a quick example…here’s a quick section of assemblers being built all the input belts are going one way, and making another section connected input to the one above is easier to do now, just copy and paste the one above…and “invert” the belt direction on each of them.
Out of curiosity I wanted to see how this function behaves with logistics towers. Apparently belts going into an towers input slot turns into an output slots. Never seen that before, but then again, never seen 

Also teleporting sorters don’t break when reversing the belt direction, which is pretty nifty.




Neutron stars:

Planet Nephogram and Cloud Effects for 12 Planet Types

Had to look up what a Nephogram is: I think it has sometime to with data that’s plotted on a curved surface…it this case it’s basically cloud cover I guess.

This cloud cover is stated to be added to to 12 planet types, I’ll just show the 

Frozen Tundra: White clouds and white patches of snow…yeah…and

The [   ] planet with the sulfuric acid oceans? So the clouds are actually orangish sulfuric acid?

Jet Effects to Neutron Stars 

Since a neutron star is spinning it would emit a powerful magnetic field far stronger than that of the earth and thus radiation and particles would be streamed out of the poles. 

Remade Scarlet Ice Lake

Ray Receivers have a visual effect

So our ray receiving planets look even cooler (matrix vibes)

Changes and Bug Fixes