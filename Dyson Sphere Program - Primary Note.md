---
date: 202105211049
areas: DysonSphereProgram
title: 
description: 
updated: 
---
# Relationships
- is a [[Factory Game]] (updated: 202410201941)

# Notes
- Game was purchased from steam on [20210309](https://mail.google.com/mail/u/0/#inbox/WhctKJWQhnBrrbGwsBbsSzFsNlPwjKPzTBNWhCgcdZLlpSTTTJpwZCcprfBTpNMnGTZCGvq)

- [DSP - Spreadsheets](https://docs.google.com/spreadsheets/d/1a3z_5vsYJZ32GWuoQntbS85LfMoE2b8TCtxQk0Ov2xU/edit#gid=189690236) - My main spreadsheet notebook
- [excalidraw](https://excalidraw.com/) - draw stuff in a whiteboard, save stuff in a notebook
## Forums
- [DSP - Reddit](https://www.reddit.com/r/Dyson_Sphere_Program/new/) - reddit forum
- [DSP - Discord](https://discord.com/) - discord forum 
- [DSP - Twitter](https://twitter.com/search?q=Dyson%20Sphere%20Program&src=typed_query&f=top) - twitter feed
- [DSP - Steam - Discussion](https://steamcommunity.com/app/1366540/discussions/0/) - steam forum
## Tools and Statistics
- [DSP - Factoriolab](https://factoriolab.github.io/dsp/list?v=11) - production calculator
- [DSP - Steam Charts](https://steamcharts.com/app/1366540) - stats of game players
- [Dyson Sphere Blueprints](https://www.dysonsphereblueprints.com/) - website of shared blueprints
## Videos
- [DSP - YouTube - Today](https://www.youtube.com/results?search_query=Dyson+Sphere+Program&sp=CAMSBAgCEAE%253D) - posted videos from 24 hours
- [DSP - Nicovideo](https://www.nicovideo.jp/search/Dyson%20Sphere%20Program?ref=watch_html5) - Japanese players (kinda outdated)
- [DSP - YouTube - 7 Days](https://www.youtube.com/results?search_query=Dyson+Sphere+Program&sp=CAMSBAgDEAE%253D) - posted videos from 7 days
## Gameplay Data

- [MMM (91481670-64-A10)](https://docs.google.com/spreadsheets/d/1ctEijf13vCVhHTrIMrfoT6JeyZevpt1ZbqtTeJizQgA/edit#gid=0) - old run data of my 3rd run
- [226](https://docs.google.com/spreadsheets/d/1eTupWV0UGnjqlWQJT81PPbABOv75XzaDx7eoCrdrqxc/edit#gid=1640015783) - old run data of my 3.5 run

## Shared Blueprint Books
- [Blueprints Readlike a Book](https://drive.google.com/drive/folders/1zomvwVj7qHOx9Q4Q1h_SggU7YLT2pXEN?direction=a) - By Tholan
- [All Plans](https://drive.google.com/drive/folders/1muiGUG6paHdMeifYvbrLXMPq9wQhFPIj) - By Cerickson
- [Blueprints](https://drive.google.com/drive/folders/16DZr8UnRdg5nWd1B7vo9tTbAMLRfM5Da) - By Nilaus

## Scrapbook Folder
- [202410201941 Dyson Sphere Program (DSP)](file:///D:%5CScrapbook%5C202410201941%20Dyson%20Sphere%20Program%20(DSP))

## YouTube
- 20210615 - [# How To Start Dyson Sphere Program Properly (Beginner Tips)](https://www.youtube.com/watch?v=yZHim3zwvYk) - Random tips from Dragonmore Gaming





# Summary:
- Dyson Sphere Program is a sci-fi simulation game with space, adventure, exploration and factory automation elements where you can build your own galactic industrial empire from scratch.
- This is my attempt to organize all my internal notes about Dyson Sphere Program here.
- Goal is list the "status" of the tip or idea. How hot or cold the tip or idea is will dictate it's value for me to share it (regardless if it has been already or not.

# Contents
- [Publishing Ideas]
- [Strategic Problems]
- [Tactical Problems]


# Main Saves
- 3rd Save, Seed: 91481670, Resources: 1.0
- 4th Save, Seed: 91481670, Combat Difficulty: 10X
- [DSP - One Planet Challenge] 



# Videos In Progress
- [Patch Update - February 1, 2024]



# Video Ideas

# Completed
- [I showed an example for Yellow Cubes...It Sucked.]
- [Yellow Cubes...An Example]
- [Early Game in Dark Fog MAX]

# Canceled
- [DSP - Prview of Dark Fog Combat System]

# Common Topics
- [Where to Smelt]
- [DSP - Hydrogen]

# Memes
- When Players Think the New Turrets Are Bad: Lasers??? HAHAHA
- That Time Satisfactory Introduced Blueprints: Drinking Girls Laughing


# Questions
- Is reform refine better performance than refine at infinite resources?
- A way to make space maps to show optimal factory group placement?
- Yellow cube with no hydrogen backproduct??? (By badpeteno)
- Bots to go instantly available?)
- Objectivity in sandbox games. Is tt is both a blessing and a curse for sandbox games like DSP?
- Regardless of how it works, is it our job to "optimize" on our end?


# Notes

- Flamehaze: all storage does is delay the bottlenecks. Have as little of it as possible
- Factorio vs DSP: I think you hit the nail in the head...this I think is a key difference between factorio and dsp, which is the item structure. In factorio many items are made independently of each other whole. In DSP, basic items are relied on multiple times. This increasing dependence would cause you to revisit that production at best or cause cascade failure at worst
- [Traffic Monitors]
- [Setup a Planet for Mining]
- [Seed Selection]
- [DSP - Mall]


# Other Posts
- [Dyson Sphere Program Guide - Tips and Tricks](https://www.amazon.com/Dyson-Sphere-Program-Guide-Tricks/dp/B08XXVJV3V) - Some extremely obscure online book on the game. It was mostly bombed due to lack of expected content and unoriginality


# References
## References - Videos
- [Mrrvlad - Dyson Sphere Program 10h Run]
- [Mrvlad - Dyson Sphere Program 3000% Start]



## References - Reddit Posts
- [The four ways to design malls](https://www.reddit.com/r/Dyson_Sphere_Program/comments/126mj21/the_four_ways_to_design_malls/) - by Steven-ape
- [Creating your own "from ore" blueprints](https://www.reddit.com/r/Dyson_Sphere_Program/comments/10q7wkj/creating_your_own_from_ore_blueprints/) - Another crazy good article from Steven-ape
- [Now That's What I Call Scale](https://www.reddit.com/r/Dyson_Sphere_Program/comments/14xwa28/now_thats_what_i_call_scale/?utm_source=share&utm_medium=android_app&utm_name=androidcss&utm_term=1&utm_content=1) - Basically managed to do 404k SPM over 10 min
- [27K Science from Raw Materials per Minute On a Single Planet!](https://www.reddit.com/r/Dyson_Sphere_Program/comments/10pbc39/27k_science_from_raw_materials_per_minute_on_a/?utm_source=share&utm_medium=android_app&utm_name=androidcss&utm_term=1&utm_content=1) - Black Box White Science 3420 x 8 by Aorus Corsair
- [My Cluster is a Little Busy](https://www.reddit.com/r/Dyson_Sphere_Program/comments/wh6i8p/my_cluster_is_a_little_busy/?utm_source=share&utm_medium=android_app&utm_name=androidcss&utm_term=1&utm_content=1) - By Big_Curious
- [1Mhash/sec Save file - Measure on Various Hardware](https://www.reddit.com/r/Dyson_Sphere_Program/comments/xm1wlt/1mhashsec_save_file_measure_on_various_hardware/?utm_source=share&utm_medium=android_app&utm_name=androidcss&utm_term=1&utm_content=1) - by MrrVlad
- [300/s Universe Matrix](https://www.reddit.com/r/Dyson_Sphere_Program/comments/srvbdo/300s_universe_matrix/?utm_source=share&utm_medium=android_app&utm_name=androidcss&utm_term=1&utm_content=1) - By Geo_Byte
- [(2) Minimum resources needed to supply an infinitely-running factory (The Mathematics of Veins Utilization) : Dyson_Sphere_Program](https://www.reddit.com/r/Dyson_Sphere_Program/comments/11x6400/deleted_by_user/) - The user deleted this post...I don't know why. May even serve for me to screen shot these pages because they can be deleted later!







# Log

- 202306290001: [Why Use Other Applications for DSP?] Factory Games like Dyson Sphere Program start at small and simple, but over time the base building game becomes much too complex, too complex for our simple minds to manage. Therefore management mistakes happen at this stage that takes the fun out of the game, and discourages further playing in the save. I feel this part of why we don't see such large saves like DSP - 200K. Would it be more fun knowing you don’t have to always think about what the heck you are doing? Tools are needed, but the vanilla game and even mods (as of this writing) doesn't provide all of the tools needed. This is where other applications that are strangely related to the game and even management in general could be used. - Obsidian - 00 Columns Sorted3 Google Tasks TickTick ToDoIst Trollo Taskade
- 202412041235: [I Like the Theme of Constructing a Dyson Sphere] DSP is literally the first factory game I played. There are a few "minigames" and "puzzles" on factories, logistics, and strategic planning, but I think the real grabber for me is that idea of a Dyson Sphere being made. There are not many games with this specific theme at all so it only makes me more attracted to these kinds of games...
- 202411201145: from 202411200820 the VOD was NOT saved from [Omniconda] stream. The next time I see something interesting in the stream I need to screenshot it because now I just have to go with my memory of what happened
- 202411200820: Spotted Omniconda attempting some "Dark Fog Drop" run. Remember to dl his video (there should be one) of his stream today
- 202411050855: Rough night last night. I totally felt like my blueprints were in complete disarray. Actually they are. I had to switch most of them as the factories they are using are no longer top tier...or does that make it useless? I'm not sure. Needless to say there is alot of work to do. And currently I just don't have the time (or know how to make the time for) to do it.
- 202411010715: DSP-Task Paste my [Strange Matter] blueprint...get it to work better even with mixed outputs
- 202411010601: Need to merge my DSP notes back into the fold...and restore my old blueprints. Look, if it the merge doesn't work out, I can always just as easily separate them again...
- 202502111024: Drafted a [Circuit Board + Magnetic Coil 360m] blueprint. This is usually applied after reaching a critical number of buildings (smelters and assemblers) even at 150 elements, it is still slow to build. I may decide to build this from scratch but I'm thinking about it.
- 202502111022: Drafted a [Wind Turbine and BAB] blueprint. It contains 74 wind turbines with a BAB in the center. The BAB is made first and launches it's construction drones, and then I can just leave! This is particularly time saving in the very early game where construction drones are very slow
