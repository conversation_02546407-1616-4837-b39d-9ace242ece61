---
areas: DysonSphereProgram
date: '202305140948'
title: 
description: 
updated: 
---
```vid
https://youtu.be/SWMeHkCGgqY?si=CTKQq-nlSWo2fRxY
Title: Dyson Sphere Program 10h run, minimal resources. Random seed, no save, generic blueprints, 2x speed.
Author: Mr<PERSON><PERSON><PERSON>
Thumbnail: Attachments/YouTubeThumbnails/SWMeHkCGgqY.jpg
AuthorUrl: https://www.youtube.com/@MrrVlad
```

# Author's Description:
- This was originally streamed. Meant to be a "casual" run, trying to see the least effort needed to get the achievement while playing as normal as possible. A total of 13 blueprints have been placed. The seed was easy - starting area is good with all resources close, all resources are in sufficient quantity in the system. No fireice. Took 9-28. Since it was 10h uninterrupted gameplay, started making mistakes that costed at least 30min. Built all science to 180/min. Purple and green could have been at 120 or less. 0-40 starting mall and blue. Should have been running at 0-25. 1-15 red to 90/min. 1-45 red to 180/min 2-00 proliferator mk2 2-45 yellow. 3-55 yellow automated with ILS. 4-30 fusion using OC hydrogen. Could be done around 3-30 using oil hydrogen. 5-40 purple 6-10 fire ice tapped. 6-20 proliferator mk3 6-40 sails 7-30 (7-50) green 8-00 started shooting sails.
- Blueprints: [https://www.dysonsphereblueprints.com...](https://www.youtube.com/redirect?event=video_description&redir_token=QUFFLUhqbG9ZUzVVTG1jQ0dyczhadzBzX3VnYWl1aTlEZ3xBQ3Jtc0ttdWdFVUkxN2daWjVocnh3UTBKMFlnNktFUXF6Y3ZrbEJnc1FXVW9sMUVRc2otUDVPV1pKMzVvblUwQ1JoZ0FNUXhndjFoZmdaRGdaNlN3Vi11OEZWeUZCMEJCQUtpVEFLNVYzcUtWODBiTHZzU3RYRQ&q=https%3A%2F%2Fwww.dysonsphereblueprints.com%2Fcollections%2Fearly-game-d516ab23-3db3-4f83-960b-53b3396826a8&v=SWMeHkCGgqY)

# General Notes:
- This is a very good example of a run through very early game buildings, what components to make, technolgies to get (and not to get), and in what order and in what times to do them in. 
- Also it's more fun to go through with notetaking here we go...

# Timeline Notes:
- 5:30 RESEARCH: Electromagnetism, Smelting, Assembly, Logistics, Core. 
- 5:30 Not a big deal but the three smelter giveaway was used for 2 iron and 1 magnet with a copper being made latter. Proceeds to cut trees.
- 7:00 First board and coil there are no belts yet so they are being fed manually
- 8:00 Blueprints. first gear added some smelters qued to replicator
- 12:00 setup 5 iron...and 4 magnets and 3 copper so far
- 14:30 BLUEPRINT
- 14:30 first begining sushi components for mall the main components are easy enough to belt from the "left" also I think there was a coal miner added in there somehwere
- 18:30 RESEARCH: matrix, drive engine, plasma, exploration
- 20:00 the sushi belt is extended now..this should be able to feed around 8-9 recipes I think
- 24:00 windmill, teslsa, assembler and miner added to the mall
- 25:00 sorter adde dot the amll
- 25:40 BLUEPRINT Blue Cube
- 25:40 The blueprint is CONVENIENTLY placed on another copper/iron pair
- 33:00 the bluecube blueprint is pretty much finished, most of the time was spent finishing the labs (not automated yet) and increasing windmill power
- 37:00 RESEARCH fluid storage, smelting purification, steel, improved logistics, electrodrive, plasma refining. 
- 37:00 Note that the blue cubes are belted to seperate labs (more labs on the way). Also the first miner for stone is placed lol
- 39:00 placed down a smelter and lab assmebler...even though they are not getting glass/stone yet...
- 42:48 this is interesting. The 4x sushi inputer from the first sushi belt is copied to be used for the "second" sushi blet. I wonder what else will be added wit the stone and glass?
- 45:00 RESEARCH: energy matrix, thermal power, semiconductor material (skipped acciedentally?), drone engine, communciation control. 
- 45:00 THe other two items added to the second 4x sushi are plasma exciter and steel. The 1st sushi belt is extended with 7 blank assemblers and 2nd sushi belt.
- 48:00 energy circuit. 4 steel are added to the 2nd sushi belt
- 52:56 RESEARCH: blueprints2, environment mod, seminconductor, proliferator1. 
- 52:56 Another iron patch is tapped form far for the 4-6 steel
- 54:00 There appears to be a splitter, tank, refinery, extractor, box, lab, and smelter in the 2nd sushi circle, for the plasma exciter, the coils from the mall components blueprint is spaghitted over
- 58:40 Red motor and steel is added to different parts of the mall. the output is boxed. The red motor output is also jumped to the green sorter
- 59:38 RESEARCH: Basic chemical engineering, processor. 3 seeps are tapped and the 
- 59:38 BLUEPRINT 90m red
- 1:03:00 Stone is tapped to 3 silicon ore > 1 purified silicon smelter. This is immediately used for around 10 sprayers
- 1:07:00 Some of the inputs for the red cube blueprint are belted in. The red cube output is acutally buffered.
- 1:11:00 Solar collection and graphene are qued for resrach and collecting mall items, mech frame and inventory capacity added in
- 1:14:00 Some water pumps are replicated, traffic monitor are added to mall?
- 1:16:00 Should note that the total power generation has reached 47 MW, red cube layout completed a bit of 10 windturbe wide expansion
- 1:19:00 RESEARCH: magnetic levitation, logistics3, VU, core2
- 1:19:00 followed with even more wind turbine expansions
- 1:21:00 BLUEPRINT 90min red
- https://youtu.be/SWMeHkCGgqY?si=23D38fUGyk3vu8rm&t=2489
- 
- 
- 
- 


# Post Dark Fog Run:
- That was a close call in 23-50
- Perhaps turning off the factories will do the same thing as making more turrets.
- Even though the enemy drops the components needed for a building, it is still a good idea to get the components reserached first?
- When the turrents are dismantled, you only get the ammo that isn't "loaded" in the turret?