/* This Theme is called "Wasp" and Obsidian Theme created by <PERSON><PERSON> */

:root
{
--font-monospace: "Source Code Pro", monospace;
}

.theme-dark
      {
--button-hover-bg-color: #DAA520; /* Darker Yellow Background for Hover */
--button-hover-text-color: #ffffff; /* White Text for Hover */
      --button-bg-color: #D49335; /* Gray-Black Background */
      --button-text-color: #242424; /* Light Gray Text */   
      --background-primary:         #242424;
      --background-primary-alt:     #242424;
      --background-secondary:       #191919;
      --background-secondary-alt:   #3D3D3D;
      --text-normal:                #faf2d6;
      --text-faint:                 #bdae93;
      --text-title-h1:              #E0E0E0;
      --text-title-h2:              #E0E0E0;
      --text-title-h3:              #E0E0E0;
      --text-title-h4:              #E0E0E0;
      --text-title-h5:              #E0E0E0;
      --text-title-h6:              #E0E0E0;
      --text-highlight-bg:          #640211;
      --text-link:                  #83a598; 
      --text-a-hover:               #83a598; 
          --text-accent-hover:      #D49335;
 /* ^^^ added for hover over bracket internal links */
      --inline-code:                #83a598; 
      --code-block:                 #83a598; 
      --text-a:                     #f8c537; 
      --interactive-accent:         #f8c537;
      --text-accent:                #f8c537; 
      --text-on-accent:             #fff;
      --interactive-accent-rgb:     #f8c537; 
      --vim-cursor:                 #f8c537; 
      /*  --text-mark:                  add text-mark if needed, this should work together with 'mark' */
      --pre-code:                   #;
      --interactive-before:         #7c6f64;
      --background-modifier-border: #f8c537;
      --text-selection:            #f8c537;
      }

.theme-dark .cm-s-obsidian span.obsidian-search-match-highlight {
color: var(--text-normal);
background-color: #483699;
}

.theme-light
     {
         --background-primary:         #EDEDED;
         --background-secondary:       #C4C4C4;
          /* thick menu border */
         --background-primary-alt:     #;
         --background-secondary-alt:   #707070; /* ← ↓  corner */
         --text-normal:                #1A2023;
         --text-faint:                 #DEDEDE;
         --text-title-h1:              #D49335;
         --text-title-h2:              #D49335;
         --text-title-h3:              #D49335;
         --text-title-h4:              #D49335;
         --text-title-h5:              #D49335;
         --text-link:                  #D49335;
         --text-a-hover:               #D49335;
         --text-accent-hover:          #D49335;
/* ^^^ added for hover over bracket internal links */
         --inline-code:                #458588;
         /*  --text-mark:                  add if needed */
         --pre-code:                   #bebebe;
         /*  --text-highlight-bg:          add if needed */
         --interactive-before:         #a89984;
         --background-modifier-border: #D49335;
         --text-a:                     #961327;
         --interactive-accent:         #f8c537;
         --text-accent:                #961327;
         --interactive-accent-rgb:     #961327;
         --vim-cursor:                 #961327;
         --text-selection:             #EDC180;
         --text-white:                 #ffffff;
     }

.nav-file-title, .nav-folder-title {
    color: var(--text-normal);
}

.nav-file-title.is-active, .nav-folder-title.is-active, body:not(.is-grabbing) .nav-file-title:hover, body:not(.is-grabbing) .nav-folder-title:hover {
    background-color: var(--background-modifier-border);
    color: white;
}

.cm-s-obsidian span.cm-formatting-link {
    color: var(--text-normal);
}

.CodeMirror-guttermarker-subtle {
    color: var(--text-normal) !important;
}

button.mod-cta {
    background-color: var(--interactive-accent);
    color: var(--text-normal);
}

.vertical-tab-header-group-title {
    color: var(--text-white);
}

.cm-header-1 {
font-size: 18px;
color: var(--text-title-h1) !important;
}

.cm-header-2 {
font-size: 18px;
color: var(--text-title-h2) !important;
}

.cm-header-3 {
font-size: 18px;
color: var(--text-title-h3) !important;
}

.cm-header-4 {
font-size: 18px;
color: var(--text-title-h4) !important;
}

.cm-header-5 {
font-size: 18px;
color: var(--text-title-h5) !important;
}

.cm-header-6 {
font-size: 18px;
color: --text-normal;
color: var(--text-title-h6) !important;
}

.markdown-preview-view h1 {
font-size: 20px;
line-height: 24px;
color: var(--text-title-h1) !important;
}

.markdown-preview-view h2 {
font-size: 20px;
line-height: 24px;
color: var(--text-title-h2) !important;
}

.markdown-preview-view h3 {
font-size: 20px;
line-height: 24px;
color: var(--text-title-h3) !important;
}

.markdown-preview-view h4 {
font-size: 20px;
line-height: 24px;
color: var(--text-title-h4) !important;
}

.markdown-preview-view h5 {
font-size: 20px;
line-height: 24px;
color: var(--text-title-h5) !important;
}

.markdown-preview-view h6 {
font-size: 20px;
line-height: 24px;
color: --text-normal;
color: var(--text-title-h6) !important;
}

/*-----------------------------------------*/
/* End of main theme, extra functionality can be added below */

.markdown-source-view { font-family: var(--font-monospace)
}

.cm-fat-cursor .CodeMirror-cursor {
background-color: #f8c537 !important;
opacity: 80% !important;
width: 9px !important; 
visibility: visible !important
}

.plugin-tabs .stayopen .view-header {
border-bottom: 2px solid var(--interactive-accent)!important;
}

.plugin-tabs .mod-root.workspace-split.mod-vertical div.workspace-leaf:not(.stayopen) > .workspace-leaf-content > .view-header .view-header-title::before{
background-color:transparent!important
}

.plugin-tabs .mod-root.workspace-split.mod-vertical .workspace-split.mod-vertical > div.workspace-leaf:not(.stayopen),
.plugin-tabs .mod-root.workspace-split.mod-vertical > div.workspace-leaf:not(.stayopen){
border-radius: 5px 5px 0px 0px!important;
}

.plugin-tabs .mod-root.workspace-split.mod-vertical div.workspace-leaf{
border-color: var(--background-secondary-alt)!important;
border-bottom-width: 0px!important;
border-right-width: 0px!important
}

.theme-light.plugin-tabs  .mod-root.workspace-split.mod-vertical div.workspace-leaf{
border-color: var(--background-secondary-alt)!important;
border-left-width:3px!important;
border-bottom-width: 0px!important;
border-right-width: 0px!important
}

.plugin-tabs .mod-root.workspace-split.mod-vertical > div.workspace-leaf hr.workspace-leaf-resize-handle,
.plugin-tabs .mod-root.workspace-split.mod-vertical > .mod-vertical hr.workspace-leaf-resize-handle{
display: none;
}


.plugin-tabs .mod-root.workspace-split.mod-vertical div.workspace-leaf .view-header{
border-left-color: transparent!important
}

.plugin-tabs .workspace-split.mod-root > .workspace-leaf:last-of-type .workspace-leaf-content,
.plugin-tabs .workspace-split.mod-root > .workspace-leaf:first-of-type .workspace-leaf-content {
    border-radius: 0px!important;
}

.plugin-tabs .theme-dark .mod-root.workspace-split.mod-vertical .workspace-split.mod-vertical > div.workspace-leaf.mod-active,
.plugin-tabs .theme-dark .mod-root.workspace-split.mod-vertical > div.workspace-leaf.mod-active {
border: 0!important;
border-radius: 0px 0px 0px 0px!important;
}

button.mod-cta {
        background-color: var(--button-bg-color); /* Uses variable for background color */
        color: var(--button-text-color); /* Uses variable for text color */
        font-weight: 600; /* Makes text bold but less than standard bold */

    }

    button.mod-cta:hover, button.mod-cta:focus {
        background-color: var(--button-hover-bg-color); /* Darker Yellow Background for Hover */
        color: var(--button-text-color); /* Keeps the default light gray text color on hover */
        font-weight: 600; /* Makes text bold but less than standard bold */
    }

    .vertical-tab-nav-item.is-active {
        background-color: var(--button-bg-color); /* Gray-Black Background */
        color: var(--button-text-color); /* Light Gray Text */
        font-weight: bold; /* Makes text bold */
        font-weight: 600; /* Makes text bold but less than standard bold */
    }

    .vertical-tab-nav-item.is-active:hover, .vertical-tab-nav-item.is-active:focus {
        background-color: var(--button-hover-bg-color); /* Darker Yellow Background for Hover */
        color: var(--button-text-color); /* Keeps the default light gray text color on hover */
        font-weight: 600; /* Makes text bold but less than standard bold */
    }

    body:not(.is-phone) .vertical-tab-nav-item.is-active {
      --background-modifier-hover: var(--button-hover-bg-color); /* More specific variable for hover */
      --icon-color: var(--button-text-color);
      background-color: var(--button-bg-color);
      color: var(--button-text-color);
        font-weight: 600; /* Makes text bold but less than standard bold */
    }

    body:not(.is-phone) .vertical-tab-nav-item.is-active:hover,
    body:not(.is-phone) .vertical-tab-nav-item.is-active:focus {
      background-color: var(--button-hover-bg-color);
      color: var(--button-text-color);
        font-weight: 600; /* Makes text bold but less than standard bold */
    }
    .checkbox-container.is-enabled {
      background-color: var(--button-bg-color); /* Gray-Black Background */
      color: var(--button-text-color); /* Light Gray Text */
  }

  .checkbox-container.is-enabled:hover, .checkbox-container.is-enabled:focus {
      background-color: var(--button-hover-bg-color); /* Darker Yellow Background for Hover */
      color: var(--button-text-color); /* Keeps the default light gray text color on hover */
  }

  .flair.mod-pop {
    background-color: var(--button-bg-color); /* Gray-Black Background */
    color: var(--button-text-color); /* Light Gray Text */
}

.flair.mod-pop:hover, .flair.mod-pop:focus {
    background-color: var(--button-hover-bg-color); /* Darker Yellow Background for Hover */
    color: var(--button-text-color); /* Keeps the default light gray text color on hover */
}

/** hr styles -- PREVIEW MODE */
.cm-line hr,
.markdown-preview-view hr {
  margin-block-start: 4em;
  margin-block-end: 4em;
  border: none;
  height: 0;
  border-bottom: 1px solid;
  border-image-slice: 1;
  border-width: 1px;
  border-image-source: linear-gradient(to right, transparent, var(--text-accent), transparent);
}

.cm-line hr::after,
.markdown-preview-view hr::after {
  /* content: '\1f41d'; */
  display: inline-block;
  position: absolute;
  left: 50%;
  transform: translate(-50%, -50%);
  transform-origin: 50% 50%;
  padding: 0.5rem;
  color: var(--text-sub-accent);
  background-color: var(--background-primary);
}
