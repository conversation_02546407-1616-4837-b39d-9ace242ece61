/* 1. Edit Mode / Live Preview (CodeMirror 6) */
.markdown-source-view.mod-cm6 .cm-em a,
.markdown-source-view.mod-cm6 .cm-em .cm-hmd-internal-link,
.markdown-source-view.mod-cm6 .cm-em .cm-hmd-external-link,
.cm-em .cm-formatting-link,
.cm-em .cm-hmd-internal-link,
.cm-em .cm-hmd-external-link {
  font-style: normal !important;
}

/* 2. Source Mode fallback (older CodeMirror) */
.cm-s-obsidian .cm-em {
  font-style: normal !important;  /* turn off italics on any emphasized text in edit mode */
}

/* 3. Reading/Preview Mode */
.markdown-preview-view em a,
.markdown-preview-view em .internal-link,
.markdown-preview-view em .external-link,
.markdown-preview-view .internal-link em,
.markdown-preview-view .external-link em,
.markdown-preview-view .internal-link,
.markdown-preview-view .external-link {
  font-style: normal !important;  /* catch any direct link styling in reading view */  
}

/* 5. Force all links to have normal font style */
a, .internal-link, .external-link {
  font-style: normal !important;
}

/* 6. Target text within brackets in editor mode */
.cm-s-obsidian .cm-link,
.cm-s-obsidian .cm-hmd-internal-link,
.markdown-source-view.mod-cm6 .cm-link,
.markdown-source-view.mod-cm6 .cm-hmd-internal-link {
    font-style: normal !important;
}

/* 4. General link reset (if theme styles links directly) */
.markdown-preview-view .internal-link,
.markdown-preview-view .external-link {
  font-style: normal !important;  /* catch any direct link styling in reading view */
}
