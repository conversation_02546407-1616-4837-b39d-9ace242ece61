<%*
tp.hooks.on_all_templates_executed(async () => {
  // get current date/time in YYYYMMDDHHmm using Templater’s date module
  const now = tp.date.now("YYYYMMDDHHmm");
  // find the current file in the vault
  const file = tp.file.find_tfile(tp.file.path(true));
  // atomically read, modify, and save the frontmatter
  await app.fileManager.processFrontMatter(file, (frontmatter) => {
    frontmatter.updated = now;
  });
});
%>