---
areas: DysonSphereProgram
date: '202312301349'
title: 
description: 
updated: 
---
Title: Making a Quick Yellow Cube Factory From Raw 






Transcript:

Hello everyone and welcome to Dyson Sphere Program

Rise of the Darkfog

That was first zoom in...pretty cool huh?

I'm assuming there has been some increased interest in this source of frustation

so we probably got some fresh meat in the frey huh?

and that also means...means...learn through the game as much as possible and get completely frustrated at stage yellow.

That's understandable. Stage blue was just a one step factory. Easy right? !!!

Stage red is ALSO...a one step production. Easy peasy. !!!

Stage yellow is I think up to 4 steps? Yup that's quite a jump. !!!

But don't worry I'm not here to lick your tears. I'm here to help.

This video I'll will be demo'ing a design of yellow cubes from scratch and from raw.

Goal of the story is well wind turbines is not quite doing it for me.

and I'm not going to get into solar panels

but fusion power...that's what I like. This is what I'm going for as soon

This is mostly for people who have gone through the beginning lessons of the game and are interested in starting black box blueprints.

We're going to be doing this design in sandbox mode. It's totally fine to do these designs in-game but for me it's a bit distracting to do this while there's fighting right at my doorstep.

Also, there is plenty of room to build here too. Relatively speaking Yellow cubes don't take much room but you simply don't know how big your next design can be until it's too big. Believe me I know. !!!

I'll also be using an online factory calculator. The factoriolab by Doug Broad is the best that I have seen by far. 

And it's ALREADY been updated with the Dark Fog Update which is absolutely profound.

I'll leave a link to it below for you play with if you find it find it useful please support the developer for keeping this tool maintained for this game for two years now! https://factoriolab.github.io/list?s=dsp&v=9

I'm not going to cover how to use it here. But basically you just click on Add items and set the amount of item you want made.

In this case I want yellow cubes...

Also...let's address an elephant in a room. Proliferation. We're going to put all of our stuff through spray coaters a pretend green color to get a mix of extra product or factory speed up bonuses.

Yes, it adds some compactness albeit more complexity to your factory. Yes, I am going to do it here. 

Yes, if haven't seen this mechanic yet...you are going to learn now. That's like...the eighth rule of DSP club? Or something like that?

All of this to say...we now have a list of the number of factories needed for this particular production chain...

I am slightly playing around with the type of spraying. Usually you want everything to be extra product to save the amounts of resources used but here I just want to get the lowest number of factories here.

Looks like we'll be putting out 38 factories. For comparison without proliferation we would have to put out 50 factories and a bit more resources to produce at the same rate...so that's basically 25% off.

Let's put out the needed factories...I'll start at the top and work my way down the list. 

Naturally the products are arranged above the inputs.

It's very helpful to memo on the belts what is supposed to go on it.

Here's the list of factories again. I'll be going back to this often. 

All of the factories needed for a specific item are rounded up. This is very early in the game so perfect ratios aren't really a thing for me yet.

So that precisely 6.667 matrix labs just becomes 7. Simple.

Let's put down 4 factories of titanium crystal. 2 belts in and 1 belt out. OK I fail at this game.

Now let's put down 4 factories of organic crystal. Also let's try to line these up a bit more. Nobody peeking!

3 in and 1 out. 

Next is 3 factories of plastic. 2 in and 1 out this is so easy!

Again everything is proliferated. 

Now we're at the smelters for titanium ingots, diamond, graphite

Now refineries. COmpared to square shaped assmeblers and smelters therear ea bit wierd, but I come up with a way to make look ok to me. 

Finally the facotries for making the spray. They are made here in the bottom so it can all be destributing botom up...

OK all the factories are up and quick check of sanity and we have the correct number of factories in the field now.

Now I'm going to stick out the spray coaters. Here all the coaters line up so there can be a straight line of spray feeding them all. Very clean.

As my general rule I only spray inputs and end products but you are more than welcome to take "spray all the things motto" litterally

Now the fun part...let's link all the inputs and outputs together. Or making the pasta as many of us would say.

One thing I do here is extend the raw input far out to make it stand out.

The steps are are close tother are easy to link as well.

Some steps are provided fro other steps...for those splitters are used.

There will be some belt sloping but again there is plenty of room here.

let me check the raw ammounts needed again and oh my gosh...the coal takes up more than yellow belt.

Even worse...it's so loptsides, one of the two steps is using 1.2 belts while the other other is using like 0.6 belts.

Well I'm not putting up with that So I'll just use some pilers to pack in 2 belts into 1. 1 splitter takes care of the rest.

Now I hook everything up to a test source and turn everything on

It does take a while for all the spray coaters to fill up with spray...but after that is done we get about 60 per minute of yellow cubes which is exactly what we're looking for...

Now I can just put this baby in my main save...yee haw!

and for you...you've seen an small example of the black box process, and hopefully won't be daunted as much at this stage.

Belive me it gets harder later.