---
date: 202405132343
areas: DysonSphereProgram
title: 
description: 
updated: 
---
# Relationships
- 202401021201: A [[Regular Update (DSP)]] was posted on February 1. Patch Notes V0.10.29.28154. The most relevant is the introduction of the [[<PERSON>le Sorter]] and the downgrade of [[Sorter Mk3]]

# Notes
- 202402012120: [Backwards Pile Sorters For 4 Stacks] ChinaShopBully shows that putting an pile sorter backwards on its own belt will do the same thing as a set of pilers, stacking 4 single items into piles of 4 items each [reddit](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1agsqj4/pile_sorter_piler_one_backwardsfacing_pile_sorter/)



## solitarybikegallery: UPS Efficiency of New Sorters vs Pilers
- https://www.reddit.com/r/Dyson_Sphere_Program/comments/1aiee59/wanted_to_test_the_ups_efficiency_of_using_the/
- solitarybikegallery tested thousands of sorters to pilers to determine performance costs. It appears the sorters are better
## Build_Everlasting: Mk3 Belts vs Pile Sorters
- https://www.reddit.com/r/Dyson_Sphere_Program/comments/1aigor2/mk3_belts_vs_mk4_sorters_part_2_of_an_experiment/
- https://www.reddit.com/r/Dyson_Sphere_Program/comments/1ah6na1/belts_unnecessary_pilers_useless_mk4_sorters_ftw/

## The Dutch Actuary: This update will make you redesign your blueprints!
```vid
https://youtu.be/0hIaU9Jax1g?si=necf5mI3wB-KxxEx
Title: This update will make you redesign your blueprints! | Dyson Sphere Program
Author: The Dutch Actuary
Thumbnail: https://i.ytimg.com/vi/0hIaU9Jax1g/mqdefault.jpg
AuthorUrl: https://www.youtube.com/@TheDutchActuary
```
- GPT: A new update for a 2D program was released unexpectedly, introducing useful features with minimal attention.
- GPT: The update introduced a new tool called the pile sorter, which is faster and more efficient than the mark 3 sorter, capable of stacking items from belts more effectively.
- GPT: Pile sorters have upgradeable load and unload stacks, accessible via the technologies screen, enhancing their stacking capabilities without the need for a complicated setup.
- GPT: The update includes an inventory expansion upgrade, allowing players to carry more materials for planet-wide blueprints.
- GPT: Combat system enhancements include throwable items with specific slots for ammunition and non-ammunition throwables, offering new combat tactics.
- GPT: The introduction of new turrets, including the jammer tower which slows down enemies, and another turret that does significant blast damage.
- GPT: Quality of life improvements such as optimized drone dispatching and sorting at battlefield analysis bases, ensuring efficiency in repairs and inventory management.
- GPT: The ability to interact with relay stations from the planetary map for easier navigation.
- GPT: Minor tweaks and optimizations continue to be made by developers to enhance gameplay.
- GPT: The presenter finds the pile sorter to be a significant and convenient upgrade, making previous methods obsolete and potentially replacing mark 3 sorters altogether.
- GPT: The presenter views the inventory expansion and combat enhancements as nice quality-of-life upgrades, particularly highlighting the new throwable combat feature as satisfying but questioning its utility at higher difficulties.
- GPT: The jammer tower and the new blast damage turret are seen as interesting additions, though their effectiveness and worth due to cost and specific uses are left to player experimentation.
- GPT: Overall, the presenter appreciates the unexpected update and the developers' ongoing efforts to improve the game, encouraging viewers to like and subscribe for more content.
- GPT: **Clarity**: The presentation is thorough and informative, effectively highlighting the key features and changes introduced in the update. The detailed descriptions of the pile sorter and combat system enhancements are particularly helpful. However, the presentation could benefit from a more structured format to improve clarity, such as separating sections more distinctly for each update feature.
- GPT: **Entertainment Value**: The presenter's enthusiasm for the new features and the practical demonstrations of how they work add to the entertainment value. The use of in-game examples, such as the pile sorter's efficiency and the demonstration of throwable items, engages the viewer and showcases the update's impact on gameplay.
- GPT: **Feedback for Improvements**:
- GPT: To enhance clarity, it would be helpful to introduce a brief overview or bullet points at the beginning, summarizing the main topics covered. This would set expectations and help viewers follow along more easily.
- GPT: Incorporating more comparisons between the new and old features could further illustrate the improvements and justify the excitement for the update. For example, side-by-side demonstrations of the pile sorter versus the mark 3 sorter could visually highlight the efficiency gains.
- GPT: While the presentation covers a wide range of updates, focusing on a few key changes in greater depth might provide more value to the viewer than trying to cover every minor tweak. This would allow for a deeper dive into the most impactful features, making the presentation more engaging.



## Nilaus: New SORTER, JAMMER & PLASMA TURRET
```vid
https://www.youtube.com/watch?v=CgVxUMFzeoI
Title: New SORTER, JAMMER & PLASMA TURRET | #21 | Dyson Sphere Program | Lets Play
Author: Nilaus
Thumbnail: https://i.ytimg.com/vi/CgVxUMFzeoI/mqdefault.jpg
AuthorUrl: https://www.youtube.com/@Nilaus
```
- GPT: The player is testing new features in a game, mentioning integrated logistics, automatic pilot, pine solar, Jimmy Tower, and jamming capture as new additions.
- GPT: They discuss an upgrade that allows a console to feed two stack tags and mentions a load and unload feature.
- GPT: The player conducts experiments to understand how the new features work, specifically focusing on how inserters handle items and the efficiency of upgrades.
- GPT: They observe that upgraded inserters can pick up from multiple stacks and that upgrades affect how effectively these inserters can fill belts with items.
- GPT: The player notes the introduction of a plasma turret and tests its capabilities along with energy shields and burst features.
- GPT: They express concern over changes affecting game blueprints, suggesting that updates may have broken existing setups.
- GPT: The transcript includes detailed observations on game mechanics, item handling, and the performance of new game features following updates.
 - GPT: The player seems disappointed with some of the new features, stating that certain upgrades do not improve performance as expected.
- GPT: They find the jamming tower and plasma turret underwhelming, questioning their utility and effectiveness in the game.
- GPT: The player is critical of the changes to blueprints and worries about the potential negative impact on existing game setups.
- GPT: They express a preference for missiles over the new defensive options provided by the game, deeming them superior.
- GPT: Overall, the player's tone suggests they are underwhelmed by the updates, finding them either confusing or not as beneficial as hoped.
 - GPT: **Clarity**: The presentation of new features and updates could benefit from more straightforward explanations and demonstrations. The player's confusion and need to extensively test to understand the updates indicate that the game's communication about these features might not be clear enough.
- GPT: **Entertainment**: The exploration of new features, despite the player's criticisms, adds an element of discovery and experimentation that can be engaging for viewers. However, the entertainment value is dampened by the player's frustration and disappointment with the updates.




# Script

## Introduction
- Hello everyone, this is Umabel and we're once again...back to Dyson Sphere Program.
- In early February 2024, YouthCat studio added a tinsee tiny update to their early access game.
- Tiny that is compare to the massive combat update in Decemebner 2023...but common three buildings is nothing scoff at
- Today, we'll take a look at those three new buildings. And there were some notable changes so we'll analyze those as well.
## Pile Sorter
- For sure, the most anticipated addition in this update is the pile sorter.
- Sorters are very small building attachments that are meant to transfer items between various factories, storage boxes, and conveyor belts.
- Conveyor belts especially and instead of running a single item they can be running piles of up to 4 items. While stacked belts were clearly the way the go, there were only two ways to make such piles, the automatic pilers a and logistics stations
- auto pilers were available early but were fairly large 
- logistics stations could be upgraded to output stacked belts but that only happened in very late game (like after the tutorial), and while they could make maxed-out INPUT belts could do nothing about making maxed OUTPUT belts. 
- Here I'm just putting the output belts into stations that can be repiled later on
- And many players did use automatic pilers to make those kinds of output belts, but needless to say it took a lot of effort to get such a result. And Sadly a few seconds of video doesn't show just how bulky automatic pilers are.
- But that's ok, because now we got this thing now.
- Now, pile sorters are basically the next tier of sorters. 
- In the tech tree pile sorters are unlocked next to automatic pilers
- Automatic pilers are still going to be around but they will probably be doing some obscure stuff
- That's because the pile sorters are so good, they have to go through their own upgrade path throughout the tutorial to reach their full power. And I when I mean throughout the tutorial I mean mostly done with just yellow science and one final upgrade with green science? So these guys are getting up to speed pretty early.
- Now I'll admit I don't understand the upgrades 100 percent...
- Simultaneous loading and unloading? Is this more DSP physics lore?
- But one thing this guy does understand is that they can plop down piles into belts! And if there is a pile already there and it's not 4 high, then it will pile on that pile!
- Is that not enough pile for you?
- At base level pile sorters are a bit more throughupt than blue tier three sorters. 
- And at full power pile sorters can transfer ALOT of items. 7200 per minute which is quite literally the maximum possible.
- There at two use cases that I will demo from this addition. Yes this is besides replacing automatic pilers, it works slightly different but arguably it is likely a superior replacement and is simple enough to do.
- Here's a late game iron ingot layout with the highest tier factories taking in a single fully piled belt of iron ore and outputing a SINGLE belt of fully piled iron ingots! Astounding!
- Without pile sorters the output belts would be forced to go through multiple pilers to try to increase throughput.
- Or in my case putting them straight into stations so they can be re-pilied elsewhere...either way not exactly perfect options.
- Now with some tweaking, it's totally possible to make it happen. Note that all of the sorter tiers are mixed around here which I believe is for better performance. I'm fairly sure pile sorters are more expensive to run compared to the others so don't intend to be spamming them everywhere. 
- and speaking of sorter spam box method lovers will certainly love this guy.
- Box method is where items are just being moved around through sorters and storage boxes instead of conveyor belts. They have their pros and cons which I won't get into
- Before though tons and tons of blue sorters had to be spammed between the boxes to get a high enough throughput which is to say VERY bulky.
- Now it can be done with a single pile sorter. Again since a single pile sorter has the same throughput as fully piled belt only one sorter needs to be linked between the boxes per item. Very nice.
- Here I'm just checking if could over 7200 per minute between boxes, nope. oh well.
- So anyway, yeah, pile sorters are going to be very useful in mid and endgame.
- and hopefully not just for replacing pilers

## The Turrets
- Alright, enough of that, now let's get into the fun stuff.
- For our next segment I bring you over to a save that I repurposed as a combat sandbox to test out some turret setups at least in the early game
- And what I mean by early game is engaging in wave defense by 5 level 30 bases at a rate that is typical for yellow science?
- In fact all the damage is durability is upgraded with only yellow science.
- To defend ourselves I have a layer of BABs in the front, 2 layers of laser turrets, a main turret layer, and back layer of BABs on the back that are packed with attack drones.
- And of course way back there are signal towers with like...a million missile turrets connected to it somewhere but but we don't talk about that
- Right now there is no main turret so quite simply, the BABs and combat drones soak the damage, while laser turrets finish them off.
- This results in casualties from tesla towers, BABs, and attack drones. So we'll compare this base setup with the others...
- Shell Cannons...you're up....ok you fail at life, next...
- Super Sonic Missiles, not bad at all.
- and now plasma casters, I mean turrets they're the new guys in class...you look cool but you're kinda stumbling there...
- alright Jammer Towers also new this update they go and kill the...
- wait...you don't kill the enemy....what do you again exactly?
- Oh you seem to "slow" the enemy down a bit. Hard to see...OK this is a bit different.
- So they shoot invisible jammer capsules at the enemy. And despite the very fast fire rate there is some sort of delay setting to make them more effective I suppose?
- Ok, I'll setup up a delay of 1 second and just line them all up like usual. And for good measure we'll include those suppression capsules as well. I see them being slowed but the current setup it still doesn't prevent them from getting into range and shooting their victims at least once before dying.
- OK let's let turrets do their things for 10 minutes and see how they fare
- And the results are in...they each destroy the same amount of enemies (well they have to) while I translate the casualties and ammo use by the number of factories needed to produce their replacements. 
- And from the looks of it shell cannons and supersonic missiles are doing quite well
- While the plasma turrets do much more poorly, and the jammers not surprisinly are doing nothing, and those setups are worse off than no turrets.
- The DF units are hit by the cannons and they are forcibly moved to backwards into the rest of their rest their group 
- So not only they take longer to reach their targets but now they are grouped together which sets them up for more cannon damage 
- The cannons and missiles are doing a good job of knocking back the enemy efficiently splash
- And currently that's how I seem to like it. 
- Well only time will tell, however I'll at least show you here how they stack up and why
- Eventually I throw in an endgame scenario, we're going to have 20 dark fog bases and hopefully we'll see a few SR turrets one shot splashing dozens of units at a time?
- I find this very interesting, I'll test on this further in the future
- So yeah, just as my lazy butt is trying to finish this video, YouthCat did regular update, giving noticeable improvements to the SR plasma, and jammer turrets. If only so slightly
- So I went back to the the turrets again again noted the casualties and ammo use, from what I can tell the jammer turrets got quite a buff at the very least it's not a laughing stock now. Again time will tell if will stay that way or someone comes up with a way to make them help in combat rather than hurt.


## Icarus Skills (Shield Burst and Throw Item)
- Next are the Icarus skills shield burst and throw item
- And they are aptly named, because apparently you need a whole lot of skill to use these well.
- Load the throwable item to your inventory, press z to go into combat toss it from your mech cannon style
- Throwable items include explosive units, jamming capsules...and water...
- not sulfuric acid to melt your enemies, not crude oil to soil them to death, just water...that apparently does nothing for now.
- Anyway as far as I can tell, the only thing that seems to be worth throwing is jamming capsules since there seems to be an area of effect?
- Or maybe those crystal bombs and shield burst can used to bomb planets bases?
- Well, let me break this down right now. At best it will be as good as super nova so in other words don't expect too much from it on an massive scale although you might get a few seconds of badassery...but that's just my current take on it right now.

## Hide Empty Dyson Spheres

## Inventory Capacity Level 7
- Keep running out of room in your inventory? Well you'll be happy to know that in this update a 7th upgrade to inventory capacity has been given.
- The previous upgrades gave relatively ok increase in capacity, well upgrade 7 gives quite a bit.
- Basically it's a single upgrade that gives 12 more slots on the logistics panel and a 24 more item slots in the main inventory. Not too shabby. 
- Hopefully this is not going to matter too much though with the personal space ships that will eventually come..

## BAB

- When battlefield analysis bases (some of you call them BABs, well I can't....I just can't...so I call them B-A-Bs for short, that's what I call them thank you for your understanding.
- Not only are BABs basically boxes that collect dark fog loot, they also repair and many cases replace buildings.
- To replace a building it needs to have the building in its inventory, and it can't do that if we have a unfiltered sorter taking it out with df loot now, huh?
- The devs probably saw this issue, and not surprisingly they introduced "blue slots" to the BABs.
- And the blue slots, work similarly opposite to red slots (similarly opposite similarly opposite )
- What I mean is that items that are in a blue slot can't be taken out by a sorter, but they can be filled by sorter.
- So buildings that are in a blue slot wont be messed with until they are needed to replace broken buildings. I saw that work myself.
- And as far as dark fog items go no they will not go into blue slots. Is this making sense? Great!
- In conclusion blue slots to BABs will add some convenience but for all your wannabe dark fog farmers out there there is still quite a learning curve and probably development still in process with all the BAB interactions out there, heck I'm still trying to understand box attachments and by extension distributors.
- 

## Dark Fog Relay Station Starmap (V) Interaction

## System Broadcast Volume Setting in Audio Settings

## Cargo Stacking Prognosis

## Automatic Material Cache Quantity

- Now based on producing duration and producing speed, so it's not just depending on recipe amounts anymore.
## Output per Base Tool Tips
- 00OK, so next, as far as Dark Fog Farming goes
- There's a change on the main tool tip on drop rates
-  it's basically saying that a single "base" represents 1080 kills per minute
- In my case I'm not there yet, but currently I'm killing about one third of that.
- So I should be expected only a third of stated item rate
- So let's look at the items
- So I say I'm at 123 per minute and I'm at a third of that
- So it's no more than 40 ingots per minute.
- Let's see if that true then.
- So we're getting
- about 40 ingots per minute more or less.
- Yeah it checks out. 
- That should be very nice for us nerds planning out our farms.
## Weapons to "Auto" and/or "Manual Fire"

## Redo construction and repair system???
- Oh and finally they apparently did something with the construction drones
- Usually I stop upgrading the number of construction drones because honestly the game couldn't handle anymore. 
- I think it stopped at around 24 or so after that the remaining drones just sit there
- oh course drone engine makes the construction drones go out faster but that's capped out
- Now with the changes let's try a large build but with 120 drones on hand
- well ok...now the drones that can put is significantly more than 24.
- My memory is a bit effy, but I would say the constuction of large builds have gotten better with upgrades,
- if your fps can handle it of course.

## Conclusion
- OK well there was also belt drawing and ammo changes but I can't stomach anymore about this patch. 
- I took way way way too long putting this out there. 
- But it's all practice hopefully I'll be faster next time. 
- If you made it this far, thanks for watching and I'll see you in the next update!


## Conveyor Belts Optimization

- Premade belts are more hexagonal than circle. So less vertices to draw is more easier on the GPU when doing large builds.

## Balance
## Core Element

## DF Drop Stack

## Space Shield Radius Multiplier

## Sprayed Capsules Rounded to 13

## Ammo Box Amount...Reduced Material Amount?
- Some ammo recipes have been changed.
- Notably its for improving the gauss turrets with magnum ammo using 3 copper ingots instead of 4 and super alloy boxes using 1 alloy instead of 2
## Shell Set 11m Blast, Crystal Shell Set 15m Blast

## 




