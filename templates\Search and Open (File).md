<%*
const selection = tp.file.selection();

if (!selection) {
    new Notice('No selection found.');
} else {
    new Notice(`Searching for "${selection}" in Scrapbook...`);

    const fs = require('fs');
    const path = require('path');
    const { exec } = require('child_process');

    const scrapbookDir = 'D:/Scrapbook'; // Use forward slashes for compatibility

    // Function to recursively get all files and folders in a directory and its subdirectories
    function getAllItems(dirPath, arrayOfItems) {
        const items = fs.readdirSync(dirPath);
        arrayOfItems = arrayOfItems || [];

        for (const item of items) {
            const fullPath = path.join(dirPath, item);
            const stats = fs.statSync(fullPath);

            if (stats.isDirectory()) {
                // Add directory
                arrayOfItems.push({ path: fullPath, isDirectory: true });
                // Recursively search inside the directory
                getAllItems(fullPath, arrayOfItems);
            } else {
                // Add file
                arrayOfItems.push({ path: fullPath, isDirectory: false });
            }
        }

        return arrayOfItems;
    }

    // Gather all items in the Scrapbook folder
    const allItems = getAllItems(scrapbookDir);

    // Filter items whose names include the selection
    const matchingItems = allItems.filter(item => {
        const itemName = path.basename(item.path);
        return itemName.includes(selection);
    });

    if (matchingItems.length > 0) {
        // Provide a quick notice about how many matches were found
        new Notice(`Found ${matchingItems.length} item(s) matching "${selection}". Opening...`);
        
        matchingItems.forEach(item => {
            if (item.isDirectory) {
                // Open folder
                exec(`start "" "${item.path}"`);
            } else {
                // Open file
                exec(`start "" "${item.path}"`);
                // Then open the folder containing it
                const fileDir = path.dirname(item.path);
                exec(`start "" "${fileDir}"`);
            }
        });
    } else {
        new Notice('No matching files or folders found in Scrapbook.');
    }
}

// Prevent Templater from modifying the note
tp.skip_replace();
%>
