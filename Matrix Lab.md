---
date: 202503070609
areas: DysonSphereProgram
title: 
description: 
updated: 
---
# Relationships
 is a [[Buildings]]


# Notes

## Dyson Sphere Blueprints
- 20250207 - UrLocalGuru - [## Verticle Research & Universe Matrix](https://www.dysonsphereblueprints.com/blueprints/factory-verticle-research-universe-matrix) - Uses tilted sorters to tilted belts, a solution to the universe matrix 6+1 input problem




# Log
- 202503080630: [The Six Input but One Output Problem] Most factories can take one, two, three, even up to five inputs. Even the worse designs can work by have belts run side-by-side on them for both output and input. Full white cube labs are unique in that they have seven total items going through it. Only six can run through the sides, so one sorter has to be placed in an orthogonal side of the factory. What to do? (smile)