---
areas: DysonSphereProgram
date: '202101290252'
title: 
description: 
updated: 
---
# Relationships
-  is the aspect of Dyson Sphere Program that concerns the automation of transporting items across different points without conveyor belts.

# Links


# Reddit
- https://www.reddit.com/r/Dyson_Sphere_Program/comments/1dfkltl/is_it_normal_to_have_so_many_logistic_bots/
- https://www.reddit.com/r/Dyson_Sphere_Program/comments/1depxm2/ils_not_delivering_automatically_unless_i/



Drones vs vessels vs bots

20230719






# Relationships
- Best using logistics stations in Dyson Sphere Program



# Videos
```vid
https://www.youtube.com/watch?v=GWPG5B9sPeQ
```
```vid
https://www.youtube.com/watch?v=Bym9CU5UbJA
```
- Click on them from anywhere in the planet! (OrgunUK - Gia Jo - PanzerMassX)
```vid
https://www.youtube.com/watch?v=s8H6udZIT0A
```



# Notes
- The more evenly distribute an item across multiple demand points. Give no drones/vessels to the source. 
- When the supply towers have vessels/drones available, the tower seems to favor sending it's spare items to a favored tower (not random)
- The game tends to be more random toward demanding towers than source towers.






```vid
https://www.youtube.com/watch?v=P_8dlGaX7Hc
```


# Buildings with Transportation
- [Planetary Logistics Station]
- [DSP - Buildings - Interstellar Logistics Station (ILS) and Vessels]
- [Logistics Distributor]