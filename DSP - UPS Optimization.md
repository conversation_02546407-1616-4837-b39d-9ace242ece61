---
date: "202307012129"
areas: DysonSphereProgram
category: dysonsphereprogram
title: 
description: 
updated: 
---
# Relationships
-  is a group of build styles in the game [[DSP - 00]] that are used with the goal of building the largest factory production the most concerns anything about the performance of the game measured in  (202307011822)

# Prompt
- Go through all of the ideas and notes. Try to answer some of the questions that are implied throughout the note. Try to confirm some of the assumptions that are given in the note. Expand on each of the ##headings using references you find. Provide links to references if you are able to find them. Finally revise this entire note following along with the format of the note.

# Ideas
- 202503061024: [What is the Best Piling for UPS?] A piled belt is more efficient than a non-piled belt. Stations provide piled belts when fully researched, but what about automatic pilers and pile sorters? These can "repile" outputs from output belts, but it is not certain. Which of the three are best...

- 202502270716: [What is UPS in Factory Games] UPS refers to updates per second. This is the number of ticks the factory game is able to process through per second and essentially how quickly the game can run. "Optimizing UPS" means scaling your factory while keeping UPS as high as possible. 

- 202502261743: [What is a Large Factory Production and/or Megabase in DSP] to me in DSP a factory is to keep using power and material from resources, including Dyson Spheres that are built, to create universe matrices which are ultimately "uploaded to the centre brain" for simulation. The larger the entire factory is, the more of these matrices that can be uploaded

- 202502261744: [The Two Limiting Factors of Scaling Up the Factory Production and/or Megabase] There are ultimately two limits to scaling up a factory. First there is the game space limit. Here the space that planets provide are filled up and no more factories can be place to increase production. Builds that use all space available, filling entire planets are considered space efficient. 

- 202502261750: [Tick-Time Calculation as a Limit to Factory Scale Up] Second there is game-tick time limit. As the number of entities increase more and more time is taken per game tick to calculate the next state of all entities in the game. Slowly but surely the time taken on each tick becomes significant enough to effect the frame rate until a large ammount of time is taken just to move the game to the next frame. Using entities and build styles that minimize the game-tick time is the main purpose of this note.

# Notes
## Introduction
For those of who don't know me too well (probably all of you)...my main niche and style of playing around [DSP - 00] is making performance optimzied layouts

That is? Making the biggest numbers in the game with our current hardware

That means getting an "unhealthy" amount of knowledge on how the game works.

One of those ways to simply experiement with what techniques gave the best perofrmance and which ones didn't. But there was a problem with this method

There was method to measure the actual peroframnce of the game

There is frames per second and 

But the the developers gifted us with two amazing tools to make this happen, the performance tab, and sandbox mode

I will now refer you to the performance panel…a gift given to us by the dev gods as a tool to closely observe how our factories burn CPU time

In the performance tab all of the time required to each categoery of the game logic in order to pass a single a tick of the game is listed for us to see

And in sandbox, it now much much easier to create, test, and compare different build styles

The optimization cumminity as taken these tools and have made some intriguing conclusions

The UPS Wars…Begun…It Has…

Base Builders are always a nice a thing but there is always one rule we must all follow.

The factory…must grow…

Sadly our computers have not caught up with our ever increasing hunger for simulated pixels

As Morse’s law is becoming more and a more a pipe dream

Before I begin, I will have to address a HERD of elephants in this room.

First off I don’t know what kind of computer you have. You could have the latest and greatest workstation or a potato.

Multithreading can be seen as a sort of discount on CPU time, but it’s difficult to tell what is being discounted and by how much.
## Whole Factory
 - This is most of the logic that we have control over with our building selection (along with Dyson Sphere). It starts relatively low at 50%? of the game-logic, but inevitably increases to a painful 90% as the cluster grinds to halt.

## Conveour Belt
- Keep belt lengths as low as possible. The number next to the belt tool tells you how many segments it will take to build in real time, use that!

## Power
- Each of the power poles have their pros/cons…substations are larger in size but cover more area, tesla towers are compact, and wireless power towers…well…they can charge our mechs! It seems that it’s all the same to the game-logic. In other words, the less power poles used, the better. Go with substations when able.
- There seems to be a belief that solar panels are ok. They are probably not ok. 
- First, they are grid sources so they are effectively like power poles
- Second, there is a separate logic on how much power they are generating because that depends on if they are facing the day or night side or somewhere in between.

## Drones vs Belts
- Vessels in flight consume less CPU per item than items on belts, so shorter belts with SPM might be a better tradeoff?
- Need to determine on a certain throughput basis, what length does belt/drones would have the same tick time?

## Splitters
But can often be replaced with sorters which apparently have way less coding involved. Depending on CPU situation a single splitter can take the same resources as a few dozer sorters! 

## Sorters
Rule-of-Thumb: Use the slowest sorter possible!

Just like factories an idle sorter is not so idle in the simulation. Every tick it likely doing an extensive check to see if there is a valid cargo to pick up, drop off, hold, etc.




- This was took a while to test and confirm and seems counterintuitive…but mk3 sorter is not the end-all-be-all solution for UPS. 
- This extra cost was implied by the devs when they mentioned the “item push mechanism”. 
- In short, if a mark2 sorter is all you need to transfer items to/from a belt then use it.
- For hardcore UPS…more savings to come out skip the cargo staking technology for mk3 sorters. As of yet it cannot be toggled.
- As of now, the item push mechanism was removed from blue sorters and they now work just like the green and yellow sorters.
- The best rule of thumb is to the use the sorter that is best suited for the expected throughput.
- In a hardcore perspective, We could even space out the yellow sorters to give them a smaller throughput which is great for the small items?!

## Extra Product vs Speed Up
- Proliferation was brought into a DSP as a mechanic that brought more complextity to factories, but brought the promise of big numbers
- And who doesn't want bigger number
- For the common question of Extra Product vs Speed Up usually Extra Product for everything is the very safe option.
- Typically the costs of proliferator would involve coal. And although it is a very good deal using coal to make spray, espeically mark 3 proliferator, it's not a awesome deal for every step so it's understandable if it is skipped.
- For example T1 products, like iron ingots and even nanotubes from spiniform...get a minor benefit from spaying.
- Later in the game when saving raw resources isn't as much a desire, it becomes desirealbe to optimize the number of factories used in the cluster.
- They are both great in their own rights and with the right mixture it can really help optimize use of proliferators. But if one had to be simple and HAD to choose between the two, then I would say go with extra product. The factories used are somewhat less, and there are less raw items / less intermediates / less spraying. The only drawback is the factory on extra product mode probably involves more game-logic than a simple “change the cycle time” of a speed up factory. (not to mention optimizing the setting up the factory ratio is a bit of nightmare). Regardless it’s likely the efficiency benefit will often outweigh the CPU cost.

## Random Stuff
Going off planet causes the game to not load items on belt, therefore the “cargoes” category of the game logic is skipped.

## Various Facility
- This category is “mostly” for creating the actual items. 
- This is a very important category as basically all the factories are being run through this.
- Spray coating a good idea.
- Keep them working at all times. Not only an idle factory not producing any items, the CPU time spent on is worth nearly 3 working assemblers!
- Matrix labs are actually interesting…the labs are stacked with one another and they tend to “share” items with another.
- Think of them as having sorters that they can share with each other.
- Sorters on complete steroids likely?
- Anyways, the main point here is that it is a probably best to keep all the factories working at all times. Factories that are empty will check nearly every tick if they have something to do. 
- Which takes up more time than if it were actively producing something.
- Some code involving moving items up/down a tower is also put in here. The higher this number (relative to everything else) the more time the game-logic is spending on the item creation as opposed to calculating power, sorters, storage, etc. This 
## Transportation (Logistics Stations)
Many players after being able to upgrade to a interstellar logistics station would stay that way, never again using a planetary station. This is generally a mistake because pound for pound, the ILS uses way more resources than the PLS. If players thought of it that way. They will use both kinds of towers depending on their exact needed
## Transportation (Vessels)
Do not place more vessels than is needed. Amazingly the game uses resources just to figure out how what a vessel is doing, even if it is doing nothing.
## Transportation (Drones)

Comparison response

Performance Factors

Production Goals
Scale
CPU
GPU
Proliferation
In outer space or not
Current Technology Levels
Build Style (what buildings are you actually using)
## Sushi Belts
Sushi belts are belts that contain mixed amounts of items instead of a single type of item. Since full efficiency involves completely filling belts to deliver items, this particular layout style does not help in reducing the number of filled belts. Instead, the amount of input sorters is reduced, since one input sorter can take the place or 2 or more (sometimes many more) input sorters that were assigned to input a particular item.


Yes, I think ultimately the comparison would be more meaningful if the factors behind them were more transparent and perhaps even more aligned. In factorio UPS wars, the production goals are the same, the saves are all run on the same hardware, and it’s even on the same map/seed. At that point it was easy to tell which save had the highest performance.

References and Testing:
Is Sushi Belt UPS Optimal?

## Combat?
There are two kinds of "farming" to consider here.

Both involve farming in someway but let me explain

The first involves farming for items that go into research...

Simulating through the deaths of 100 foggers for one ingot is quite alot compared to jiat making the factories

On the other hand getting a few high tier items like quantum chips, gravity lens, ect could be worth making farm for

But there are mark ups as well...and transporting each of the items add to the cost.

Just have to compare. And standard tests for data hasn't been made up yet or done.

The theres the other part of farming for the building materials. 

Technically only a finite ammount of the materials is needed. After which the farming can be kept to a bare minimum, if at all.

I think it's somewhere along the line, depending the planet and location.

Perhaps if a high tier planet has room to kill 1000 foggers per minute for 50 quantum chips per minute...maybe it's ok?

Otherwise, kill all foggers after getting a sizeable number of buildings needed for the capped megabase.

## Piling
A piled belt is more efficient than a non-piled belt. Stations provide piled belts when fully researched, but what about automatic pilers and pile sorters? These can "repile" outputs from output belts, but it is not certain. Which of the three are best...

## Reddit
- 202501120225 - [Where is the performance bottleneck? (not CPU thread limited)](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1hzhh5b/where_is_the_performance_bottleneck_not_cpu/) - Player states his game is decreasing in FPS. There is a link to a "popular" save for performance benchmarking provided.






Using splitters

Storage boxes

“Double Sided” Belts

On that topic using “straight” belts

PLS / ILS

Power poles VS satellite stations

Using pilers

Blue sorters VS green sorters

Number of drones in a station

Number of vessels in a station


# Relationships
- [Updates Per Second (UPS)] is the measure of game or state updates that performed per second.
- This is distinguished from [Frames Per Second (FSP)] as that is typically a Graphics Processing Unit (GPU) dependent metric, while Updates Per Second is typically a Computer Processing Unit (CPU) dependent metric
- This metric is extensively researched in the context of Dyson Sphere Program as it is a very important metric is [Megabase Building]







# Introduction
- The UPS Wars…Begun…It Has…
- Base Builders are always a nice a thing but there is always one rule we must all follow.
- The factory…must grow…
- Sadly our computers have not caught up with our ever increasing hunger for simulated pixels
- As Morse’s law is becoming more and a more a pipe dream

I will now refer you to the performance panel…a gift given to us by the dev gods as a tool to closely observe how our factories burn CPU time

Before I being, I will have to address a HERD of elephants in this room.

First off I don’t know what kind of computer you have. You could have the latest and greatest workstation or a potato.

Multithreading can be seen as a sort of discount on CPU time, but it’s difficult to tell what is being discounted and by how much.

# Idle vs Working
- Idle factories are WORSE than working factories!

# Whole Factory
- This is most of the logic that we have control over with our building selection (along with dyson sphere). It starts relatively low at 50%? of the game-logic, but inevitably increases to a painful 90% as the cluster grinds to halt.

# Conveyor Belt
- Keep belt lengths as low as possible. The number next to the belt tool tells you how many segments it will take to build in real time, use that!

# Power
- Each of the power poles have their pros/cons…substations are larger in size but cover more area, tesla towers are compact, and wireless power towers…well…they can charge our mechs! It seems that it’s all the same to the game-logic. In other words, the less power poles used, the better. Go with substations when able.
- There seems to be a belief that solar panels are ok. They are probably not ok. 
- First, they are grid sources so they are effectively like power poles
- Second, there is a separate logic on how much power they are generating because that depends on if they are facing the day or night side or somewhere in between.

# Drones vs Belts
- Statement: Vessels in flight consume less CPU per item than items on belts, so shorter belts with SPM might be a better tradeoff.
- Need to determine on a certain throughput basis, what length does belt/drones would have the same tick time?

# Splitters
- But can often be replaced with sorters which apparently have way less coding involved. Depending on CPU situation a single splitter can take the same resources as a few dozer sorters! 

# Sorters
- Just like anything else they work more with more throughput.
- This was took a while to test and confirm and seems counterintuitive…but mk3 sorter is not the end-all-be-all solution for UPS. This extra cost was implied by the devs when they mentioned the “item push mechanism”. In short, if a mark2 sorter is all you need to transfer items to/from a belt then use it.
- For hardcore UPS…more savings to come out skip the cargo staking technology for mk3 sorters. As of yet it cannot be toggled.

# Extra Product vs Speed Up
- For the common question of Extra Product vs Speed Up my answer is usually a callus “yes”. They are both great in their own rights and with the right mixture it can really help optimize use of proliferators. But if one had to be simple and HAD to choose between the two, then I would say go with extra product. The factories used are somewhat less, and there are less raw items / less intermediates / less spraying. The only drawback is the factory on extra product mode probably involves more game-logic than a simple “change the cycle time” of a speed up factory. (not to mention optimizing the setting up the factory ratio is a bit of nightmare). Regardless it’s likely the efficiency benefit will often outweigh the CPU cost.

# Random Stuff
- Going off planet causes the game to not load items on belt, therefore the “cargoes” category of the game logic is skipped.

# Various Facility
- This category is “mostly” for creating the actual items. 
- This is a very important category as basically all the factories are being run through this.
- Spray coating a good idea.
- Keep them working at all times. Not only an idle factory not producing any items, the CPU time spent on is worth nearly 3 working assemblers!
- Matrix labs are actually interesting…the labs are stacked with one another and they tend to “share” items with another.
- Think of them as having sorters that they can share with each other.
- Sorters on complete steroids likely?
- Anyways, the main point here is that it is a probably best to keep all the factories working at all times. Factories that are empty will check nearly every tick if they have something to do. 
- Which takes up more time than if it were actively producing something.
- Some code involving moving items up/down a tower is also put in here. The higher this number (relative to everything else) the more time the game-logic is spending on the item creation as opposed to calculating power, sorters, storage, etc. This 
- Transportation (Logistics Stations)
- Many players after being able to upgrade to a interstellar logistics station would stay that way, never again using a planetary station. This is generally a mistake because pound for pound, the ILS uses way more resources than the PLS. If players thought of it that way. They will use both kinds of towers depending on their exact needed

# Transportation (Vessels)
- Do not place more vessels than is needed. Amazingly the game uses resources just to figure out how what a vessel is doing, even if it is doing nothing.


# Transportation (Drones)

# Comparison response

# Performance Factors

Production Goals
Scale
CPU
GPU
Proliferation
In outer space or not
Current Technology Levels
Build Style (what buildings are you actually using)

Sushi Belts
Sushi belts are belts that contain mixed amounts of items instead of a single type of item. Since full efficiency involves completely filling belts to deliver items, this particular layout style does not help in reducing the number of filled belts. Instead, the amount of input sorters is reduced, since one input sorter can take the place or 2 or more (sometimes many more) input sorters that were assigned to input a particular item.







Yes, I think ultimately the comparison would be more meaningful if the factors behind them were more transparent and perhaps even more aligned. In factorio UPS wars, the production goals are the same, the saves are all run on the same hardware, and it’s even on the same map/seed. At that point it was easy to tell which save had the highest performance.

References and Testing:
Is Sushi Belt UPS Optimal?

# Reddit
- [20240601 UPS Improvements After New Patch?](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1d4io27/ups_improvements_after_new_patch/): Despite claimed improvements questions arise based on size of game and "typical" hardware used

# Log
- 202504080553: [Black Box Versus Modular an Experiment] The ongoing debate of which of these two paradigms for late game is still ongoing. I just realized it is relatively easy to perform an experiment...I can take my third playthrough (mostly modular) and convert it to several blackbox planets. To be fair the black box would have to be designed by me so it's as efficient as possible...