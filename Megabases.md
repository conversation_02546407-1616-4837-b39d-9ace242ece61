---
date: 202307310831
areas: DysonSphereProgram
title: 
description: 
updated: 
---
# Relationships
-  are games in [[DSP - 00]] that have been extensively scaled up beyond the typical of a normal playthrough. (202411280926)




# Notes
Folder: 202411280934 Megabases (DSP)

## Reddit
- 202404240852 - [Totally final update - 370k/min](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1cbxe1j/totally_final_update_370kmin/) - by polychlorinatedbi
- 202403020915 - [250k science/min](https://www.reddit.com/r/Dyson_Sphere_Program/comments/1b4p8p7/250k_sciencemin/) - by polychlorinatedbi
- 202307271121 - [My Dyson Sphere Program Main Save So Far: 200k/min Universe Matrix Milestone](https://www.reddit.com/r/Dyson_Sphere_Program/comments/15blyo6/my_dyson_sphere_program_main_save_so_far_200kmin/) - by <PERSON><PERSON><PERSON>_
- 202308211842 - [138K/min white cubes, need a second research planet, trying to push it past 200K](https://www.reddit.com/r/Dyson_Sphere_Program/comments/15xmqyf/138kmin_white_cubes_need_a_second_research_planet/) - by dying_animal
- 202209231217 - [1Mhash/sec save file - measure on various hardware.](https://www.reddit.com/r/Dyson_Sphere_Program/comments/xm1wlt/1mhashsec_save_file_measure_on_various_hardware/)
- 202202202022 - [Achieve my goal of 200k per minute universe matrix production](https://www.reddit.com/r/Dyson_Sphere_Program/comments/sxgx1g/achieve_my_goal_of_200k_per_minute_universe/) - by mtthefirst


# Log
- 202504111729: [Megabases and Using Mods] In general most players reaching this level of scale have to used mods to keep the game playable and/or enjoyable. Such mods can be grouped into either performance saving or quality of life (QoL) mods. There are other mods that do not fit under either description and may even bend the rules of "fairness", of the achievement. In other words, making the achievement too easy. For example SeedScanner by Selsion. 
- 202502271932: [Resources are Unlimited in Ammounts But Limited in Availability] even an infinite ammount of coal is useless if there is no hydrogen nearby (for red cubes) context is everything for an optimized megabase. It is inefficient to patch a missing vein (thus the decreased rate) with another view that is light years away). The Correct resources must be grouped together as much as possible and this even applies to early game as well in terms of how easy it is to build things out.
- 202411280927: [My Level of 200k/min Universe Matrix] - There are actually two kinds of megabases for DSP, the sphere builders who want to make the biggest spheres possible, just as implied in the game name. Heck, there's even a scoreboard for the biggest baddest spheres. And the matrix uploaders, who build up as much science as possible. - tting up an end-game cluster that is uploading around 200k/min [DSP - Items - Universe Matrix|Universe Matrix]  Need to fix this page and scrape through the for my fellow 200k Players! 

