---
areas: DysonSphereProgram
date: '202307131549'
title: 
description: 
updated: 
---
The game is open world so beyond its nominal goal there is as yet no defined objective.

This is both a blessing and curse as while some players are free to expand their gameplay as much as they please, many other players don't want to go through the constant evalution of "what-is-my-objective"

This video will try to address that issue with a simple algorityhm

The game is divided

For warpers needed, the total ammmount of items made were added (even the "gases" which are normally harvested in the same system in mid game anyway)

Vessel capacity is assumed by 1000, not all are set at this ammount but it should be negligible anyway

Warpers needed and thus the green cubes will be added to the green cube supply. It may be a noticable ammount (ex. 30 will go up to 35)

It should be noted that all green cubes will first and foremost go to making warpers and then to research.

The additonal green cubes and warpers transport will increase the total throughput but given this is an addtional trip 0.045 warps a second it should not affect the calculation

Everything needs power. Whatever your system it needs to handle all the factoies to maintain the trhoughput

Note that in the calc it includes power to make the photons (as dyson sphere power in simplex mode) this should be subtracted from the total power

Also vessel power is significant and at the same time very difficult to calculatue. Usually safety buffers are suggested, 2 is an ok factor but more or less will certainly come in play depending on overall eficency

In my case (phase 30-10) there is a power of 53827 MW subtracting by (1200 x 30) gives 17,827 MW

Doubling this in an attempt to account for vessels gives 35,654 MW

This the ammount of power that needs to be delivered

In my case using deuterium power plants this will require about 59.4~60 dueterium fuel rods to be delivered.

This is added to the already existing fuel rods (40 fuel rods for rockets are 100 fuel rods with the included power)

Of course if there is left over sphere power you can use that too (not far fetched at all!) in this case only 5 fuel rods would be needed

[I should note this is acutally cool because the fuel rod planet set to make 4*30=120...which would make 30 rockets/s it can easily swtich to generating rockets instead of generating power]

note that a single rod that delivers 600 MJ (1/s = 600MW) would theoretically require 50.4 MW to make along with the resources (it doesn't count mining power) would be 600/50= 12 times energy ratio.

So basically the power input needed to tgnerate the 35,654 MW would be 2,971 MW this is 5 fuel rods/s

Is this enough? Clearly this is reiteration of increase rods, and seeing if the infrasturue is enouhg to support the input power needed.

fyi a antimatter fuel rod ratio is 7200 MW / (7325072kW - 7200000 kW) = 125,072kW = 125 mW = 57.6 ratio. not bad at all

May want to mention the resoruces burned this will affect how early antimatter can be adopted