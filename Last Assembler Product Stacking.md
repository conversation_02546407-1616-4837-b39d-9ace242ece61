---
areas: DysonSphereProgram
date: '202307190143'
title: 
description: 
updated: 
---
# Relationships
-  is a common problem in Dyson Sphere Program and some factory games where an assembler in a nearly filled line of product is unable to produce items at 100 percent efficiency because it cannot fill the remaining spaces on the output belt at 100 percent efficiency. 
- This page also covers some solutions to the problem

# Solutions
- Tip: Before you bite the bullet on your last assembler being product stacked. Make a belt intersection instead