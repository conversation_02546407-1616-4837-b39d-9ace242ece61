---
date: 202307130825
areas: DysonSphereProgram
title: 
description: 
updated: 
---




# Notes
## Main Rules for Dedicated Planets
- If a planet is orbiting a hydrogen gas giant it can be a [Casimir Crystal Planet]
- If a planet is orbiting a deuterium gas giant it can be a [Graviton Lens Planet]
- If a planet contains any Unipolar Magnet it can be a [Particle Container Planet]
- If a planet contains Iron, Copper, and Silicon it can be a [Purple Cube Planet] or [Processor Planet]
- If a planet contains Iron and Copper it can be a [Blue Cube Planet]

## YouTube

```vid
https://www.youtube.com/watch?v=lJF9vQtpWnY
Title: Optimal SMELTING for Early, Mid and Late Game | Dyson Sphere Program | Tutorial / Master Class
Author: <PERSON>laus
Thumbnail: https://i.ytimg.com/vi/lJF9vQtpWnY/mqdefault.jpg
AuthorUrl: https://www.youtube.com/@Nilaus
```

```vid
https://www.youtube.com/watch?v=seI5r8Ybhms&list=PLVUD_DfWuS8YjexO1ZzxtxVEsidiG5O8B
Title: Dyson Sphere Program / Планеты и Звёзды Всего Кластера
Author: Kotabu Shum
Thumbnail: https://i.ytimg.com/vi/seI5r8Ybhms/mqdefault.jpg
AuthorUrl: https://www.youtube.com/@kotabushum
```




# Log
- 202505230652: [I Have Created Logic Rules to Determine What to Do With Certain Planets] To help the build process become more streamlined for each cluster, I have developed a system of rules of what should be built on a planet. The rules are meant for efficient UPS setup, though this probably wouldn't work if all planets are to be developed. Strangely, with more rules, this might make a nice logic test for a LLM to go through.

- 202409151512: [The Term Artificial Planet Types was Original Coined By Mnemomeme] mentioned this terms many times in his streams, my understanding was that a player would take a planet in its natural state and turn it into something that suits them.

- 202409151505: [What are Artificial Planet Types?] where specific planet types tend to be well suited for a particular chain of production.


