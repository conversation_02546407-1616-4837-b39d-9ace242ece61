---
date: 202412071929
areas: DysonSphereProgram
title: 
description: 
updated: 
---
# Relationships
- 202412071930:  is a [[Game Updates]] revamping the sorter system and introducing the tier 4 sorter, the [[Piler Sorter]]

# Notes
Folder: 202402151051 DSP Update 20240201 (Pile-Sorter Update)
Official Patch: PATCH V0.10.29.28154
Video Title: Dyson Sphere Program | Update | Piler Sorter | Two New Turrets | BAB Improvement
## Script
 Hello everyone, we're back again to our favorite factory game, Dyson Sphere Program. 
 
 Early February, Youth Cat Studio added yet another update to their early access game. 
 
 In this video, I'll demonstrate the three new buildings introduced in that update, as well as some smaller changes that I find particularly interesting. 
 
 Let's check them out. 
 
 For sure, the most anticipated addition to this update is the pile sorter.

 Sorters are basically small building attachments that are meant to transfer items between various factory storage boxes and conveyor belts. 
 
 Conveyor belts especially, and instead of running a single item, they can be running piles of up to four items. 
 
 While running piles were a clear way of making more efficient factories, there were only two ways to make such piles. 
 
 The automatic pilers, which were fairly bulky, or out of logistics stations, which were a very late game option, and while they could make maxed out input belts, could do nothing about making maxed output belts. 
 
 Here, I'm just putting the output belts into stations to be re-piled later on
 
  and many players did use automatic pilers to make those kinds of output belts
  
  but needless to say, it took a lot of effort to get such a result, 
  
  and sadly, a few seconds of video doesn't show just how bulky automatic pilers can be.

 But that's okay, because now we got this now. Piles orders, basically the next tier sorter.

 In the tech tree, piles orders are unlocked next to automatic pilers. 
 
 Automatic pilers are still going to be around, but they will probably be doing some obscure stuff. 
 
 That's because the piles orders are so good, they have to go through their own upgrade path throughout the tutorial to reach their full power. And when I mean throughout the tutorial, I mean mostly done with just yellow science and one final upgrade with green science. So these guys are getting up to speed pretty early. But the bottom line here is that it will plop down piles into belts. And of course, if there is room to spare on that pile, then it will actually do that as well.

 Okay, base level. Piles orders. They're a bit more faster throughput than their blue tier three sorters.

 And at full power, piles orders can transfer a lot of items, instead of 200 per minute to be exact. And that's quite literally the maximum possible. Here's a late game iron ingot layout with the highest tier factories. So basically they're taking in a single fully piled belt of iron ore, and it's going to output a single belt of fully piled iron ingots.



 Now with some tweaking, it's totally possible to make it happen. Note that all the sorter tiers are mixed around here, which I believe is for better performance. I'm fairly sure piles orders are more expensive to run compared to their younger siblings. So I don't intend to be spamming them everywhere.

 And speaking of sorter spam, that brings me to the second demonstration, box method.

 Box methods basically where items are just being moved around through sorters and storage boxes instead of conveyor belts. They have their pros and cons, which I won't get into here. But before though, tons and tons of blue sorters had to be spammed between the boxes to get a high enough throughput to them, which is extremely bulky.

 Now it can be done with a single pile sorter. Again, since a single pile sorter has the same throughput as a fully piled belt, only one sorter needs to be linked between the boxes per item. Very nice.

 Or heck with it, just spam them all.

 I like the color white anyway. So anyway, yeah, pile sorters, they're going to be very useful mid and end game. And hopefully not just for replacing pilers.

 All right, enough of that. Now let's get into the fun stuff. For next segment, I bring you over to a save that I repurposed as a combat sandbox to test out some turret setups, at least in the early game. And what I mean by early game is engaging in wave defense by five level 30 bases at a rate that is typical for yellow science. In fact, all the damage and durability has been upgraded with just yellow science.

 To defend ourselves, I have a layer of BA-Bs in the front, two layers of laser turrets, a main turret layer, and a back layer of BA-Bs that are packed with tachtrons. 
 
 And of course, further back, there are signal towers with like a million missile turrets connected to them somewhere, but we don't talk about that. Right now, there's no main turret. So quite simply, the BA-Bs and combat drones soak damage while the laser turrets finish off the dark fog units. This will result in casualties from Tesla towers, BA-Bs, especially tachtrons. So we're going to compare this starting base setup with the others.

 Shell cannons, you're up.

 Okay, you failed life. Next.

 Super sonic missiles. Not bad. Not bad at all.

 And now plasma casters, I mean turrets. They're the new guys in class, so you look pretty cool, but you're kind of stumbling there. All right, Jammer Towers also knew this update. They go and kill the...

 Wait a second. You don't kill the enemy. What do you do again exactly?

 Oh, I see. You're slowing them down. Oh, I can't. It's hard to see. Okay, this is a bit different. So they're shooting invisible jammer capsules at the enemy, and despite the very fast fire rate, there is some sort of delay setting to make them more effective, I suppose.

 Okay, that's fine. So I'll set up a main line of Jammer Towers, and I'll set up a delay of one second and just, you know, line them up like usual.

 And for good measure, we'll do this also with the suppression capsules. They're the upgraded ammo. Okay, let's let all our turrets do their thing for 10 minutes and see how they fare.



 And the results are in. They each destroy the same amount of enemies, well, they have to, while I translate the casualties by the number of factories needed to produce their replacements. 
 
 While the plasma turrets do much more poorly, and the jammers are not surprisingly not doing anything. 
 
 And in fact, the setups are even worse than no turrets. 
 
 And look, it's fairly simple to see if you're observant. 
 
 The Dark fog units, they're getting hit by the turrets, and they are forcibly moved back by knockback and backwards into the rest of their group.

 So they take longer to reach their targets, and now they're even grouped together, which sets them up for even more splash damage. 
 
 The cannons and missiles are very good at doing that. While the plasma turrets are much less so. Well, only time will tell. 
 
 However, at least I'm showing you here how they stack up and why. 
 
 So yeah, just as my lazy butts trying to finish this video, YouthCat did a regular update giving noticeable improvements to the SR plasma and jammer turrets, if only so slightly. 
 
 So I went back to the turrets again and noted the casualties and ammo use. 
 
 From what I can tell, the jammer turrets got quite a buff, at the very least. It's not a laughingstock now. 
 
 Again, time will tell if it stays that way, or if someone comes up with a way to make them help in combat rather than hurt. Next are the Icarus skills, shield burst and throw item. 
 
 And they are aptly named because apparently you need a whole lot of skill to use these well. To throw item, load the throwable item into your inventory. 
 
 Press Z to go into combat and toss it from your mech, cannon style. Throwable items include explosive units, jamming capsules and water.

 Nuts of uric acid to melt your enemies, not crude oil to soil to death, just water. 
 
 That apparently does nothing for now. 
 
 Anyway, as far as I can tell, the only thing that seems to be worth throwing is jamming capsules, since unlike turrets, that seems to be an area of effect.

 Well, let me break this down right now. 
 
 At best, it will be as good as supernova, so in other words, don't expect too much from it on a massive scale. 
 
 Although you might get a few seconds of badassery, but that's just my current take on it right now. Keep running out of room in your inventory? 
 
 Well, you'll be happy to know that in this update, a seventh upgrade to inventory capacity has been given. The previous upgrades gave relatively okay increase in capacity. 
 
 Well, upgrade seven gives quite a bit. Basically, it's a single upgrade that gives 12 more slots on the logistics panel and 24 more item slots in the main inventory. Not too shabby. 
 
 Hopefully, this is not going to matter too much, though, with the personal spaceships that will eventually come.

 When battlefield analysis bases... Now, some of you call them babs. Well, I can't. I just can't. 
 
 So, I'll call them BABs for short. That's what I call them. Thank you for your understanding. BABs, basically, boxes. 
 
 That collect dark fog loot. They also repair and, in many cases, replace buildings. But, to replace a building, it needs to have the building in its inventory. 
 
 And it can't do that if we have an unfiltered sorter taking it out with dark fog loot now, huh? The devs probably saw this issue and, not surprisingly, they introduced blue slots to the BABs. 
 
 And the blue slots work similarly opposite to red slots. 
 
 Similarly opposite. Similarly opposite. What I mean is that the items that are in the blue slot can't be taken out by a sorter. But they can be filled by a sorter.

 So, the idea is that buildings that are put into a blue slot won't be messed around with until they are needed to replace a building. Or a combat drone.

 And, as far as dark fog items go, no, they will not go into the blue slots. Is this making sense? Great.

 So, as I jump into conclusions, blue slots to BABs will add some convenience. But, for all you want to be dark fog farmers out there, there's still quite a bit of a learning curve and probably development as well. Still in process with all the BAB interactions out there.

 I, for one, am still trying to understand box attachments and, by extension, distributors.

 Okay, so next, as far as dark farming goes, there's a change on the main tooltip on drop rates. 
 
 It's basically saying that a single base represents 1,080 kills per minute. In my case, I'm not quite there yet, but currently I'm killing about a third of that. 
 
 So, I should be expecting only a third of the stated item rate. So, let's look at the items.

 So, say I'm at 123 per minute and I'm at a third of that. So, it's no more than 40 ingots per minute. Let's see if that's true then.

 So, we are getting about 40 ingots per minute, more or less. So, yeah, checks out.

 So, that's cool. Basically, it's something nice for us nerds planning out bar farms. 
 
 Oh, and finally, they apparently did do something with the construction drones. 
 
 On my last save, I stopped upgrading the number of drones, because honestly, the game couldn't handle anymore.

 I think it stopped spitting out more construction drones after 24.

 Yeah, that's what I'm saying. The limit was about 24. There was only 24 out at a time, so the remaining drones would just sit there doing nothing.

 Now, the change is, let's try a large build. Let's take it out to 120 drones.

 Well, okay. Now, the drones that can be put out is a lot more than 24. And my memory is a little bit iffy, but I would say now the construction of large builds at late game is pretty much better.

 Your FPS still has to handle it, of course.

 Okay. Well, there is also belt drawing and ammo changes, but frankly, I can't stomach anymore talking about this. 
 
 Hopefully, I've inspired you, and thank you for watching, and I'll see you in the next update.


