{"type": "excalidraw", "version": 2, "source": "https://github.com/zsviczian/obsidian-excalidraw-plugin/releases/tag/2.12.2", "elements": [{"id": "EOQUROWtH28TlUfASDk6q", "type": "rectangle", "x": 612.8590532957423, "y": 175.70268607068556, "width": 615.2347581656634, "height": 623.0063928226731, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0A", "roundness": {"type": 3}, "seed": 983523265, "version": 243, "versionNonce": 868922593, "isDeleted": false, "boundElements": [], "updated": 1749644454898, "link": null, "locked": false}, {"id": "GkLJv6Vj", "type": "text", "x": 828.1521283828646, "y": 807.9528647571481, "width": 145.2734375, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0B", "roundness": null, "seed": 1915923681, "version": 98, "versionNonce": 1868431919, "isDeleted": false, "boundElements": [], "updated": 1749644481583, "link": null, "locked": false, "text": "Exporting Planet", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Exporting Planet", "autoResize": true, "lineHeight": 1.25, "rawText": "Exporting Planet"}, {"id": "bIrUqUdgYev0f4h1Cbi47", "type": "rectangle", "x": 1343.3224218569796, "y": 172.9174493946213, "width": 447.4854424977265, "height": 375.6621426450776, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0C", "roundness": {"type": 3}, "seed": 1509089057, "version": 257, "versionNonce": 1172635055, "isDeleted": false, "boundElements": [], "updated": 1749644750514, "link": null, "locked": false}, {"id": "hfeYtgZL", "type": "text", "x": 1503.8322371246215, "y": 559.8078632281255, "width": 120.5859375, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0D", "roundness": null, "seed": 1926849665, "version": 190, "versionNonce": 285381985, "isDeleted": false, "boundElements": [], "updated": 1749644798967, "link": null, "locked": false, "text": "Import Planet", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Import Planet", "autoResize": true, "lineHeight": 1.25, "rawText": "Import Planet"}, {"id": "xa9_oOpOX008v3dCb5QaR", "type": "rectangle", "x": 675.3289687427919, "y": 582.4206985076064, "width": 519.3755352663472, "height": 121.90138414222596, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0E", "roundness": {"type": 3}, "seed": 230670977, "version": 234, "versionNonce": 781598441, "isDeleted": false, "boundElements": [{"id": "aVZwPUoLgm4zfeEgCir3n", "type": "arrow"}], "updated": 1750070001183, "link": null, "locked": false, "customData": {"legacyTextWrap": true}}, {"id": "tqRPT7Hp", "type": "text", "x": 877.7381463646442, "y": 708.3512111562741, "width": 101.9140625, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0F", "roundness": null, "seed": 585717761, "version": 44, "versionNonce": 605849473, "isDeleted": false, "boundElements": [], "updated": 1749644502603, "link": null, "locked": false, "text": "BOT MALLS", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "BOT MALLS", "autoResize": true, "lineHeight": 1.25, "rawText": "BOT MALLS"}, {"id": "sHXXRJfA", "type": "text", "x": 714.4095467832873, "y": 640.0359049044031, "width": 26.62109375, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0FV", "roundness": null, "seed": 323396335, "version": 134, "versionNonce": 768176353, "isDeleted": false, "boundElements": [], "updated": 1749644544683, "link": null, "locked": false, "text": "GA", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "GA", "autoResize": true, "lineHeight": 1.25, "rawText": "GA"}, {"id": "6co9ioEz", "type": "text", "x": 774.8724862745141, "y": 638.4654015979866, "width": 25.185546875, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0Fl", "roundness": null, "seed": 1152538913, "version": 261, "versionNonce": 1829480815, "isDeleted": false, "boundElements": [], "updated": 1749644557588, "link": null, "locked": false, "text": "GB", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "GB", "autoResize": true, "lineHeight": 1.25, "rawText": "GB"}, {"id": "ZTcxqeLm", "type": "text", "x": 834.5502939296836, "y": 639.2506232969072, "width": 26.103515625, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0Ft", "roundness": null, "seed": 1332210511, "version": 315, "versionNonce": 1765139439, "isDeleted": false, "boundElements": [], "updated": 1749644608725, "link": null, "locked": false, "text": "GC", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "GC", "autoResize": true, "lineHeight": 1.25, "rawText": "GC"}, {"id": "rydmqjI6", "type": "text", "x": 892.6575982784365, "y": 637.680179899066, "width": 27.744140625, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0Fx", "roundness": null, "seed": 50301953, "version": 362, "versionNonce": 800198145, "isDeleted": false, "boundElements": [], "updated": 1749644612332, "link": null, "locked": false, "text": "GD", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "GD", "autoResize": true, "lineHeight": 1.25, "rawText": "GD"}, {"id": "gXPd2kcW", "type": "text", "x": 951.55012432611, "y": 637.680179899066, "width": 23.837890625, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0Fz", "roundness": null, "seed": 1328872591, "version": 392, "versionNonce": 552029967, "isDeleted": false, "boundElements": [], "updated": 1749644615425, "link": null, "locked": false, "text": "GE", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "GE", "autoResize": true, "lineHeight": 1.25, "rawText": "GE"}, {"id": "CEa0GBvr", "type": "text", "x": 1005.7312003631092, "y": 636.8949582001455, "width": 23.486328125, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0FzV", "roundness": null, "seed": 154952737, "version": 424, "versionNonce": 1587934433, "isDeleted": false, "boundElements": [], "updated": 1749644619548, "link": null, "locked": false, "text": "GF", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "GF", "autoResize": true, "lineHeight": 1.25, "rawText": "GF"}, {"id": "sa7rvCkMUW9Tz-KbxI4WU", "type": "rectangle", "x": 712.5597518244007, "y": 602.7807078498575, "width": 30.00006103515625, "height": 29.33331298828125, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0G", "roundness": {"type": 3}, "seed": 680836079, "version": 87, "versionNonce": 260763009, "isDeleted": false, "boundElements": [], "updated": 1749644646906, "link": null, "locked": false}, {"id": "xSd88OAfN4OgaWBVh5ez2", "type": "rectangle", "x": 773.0226913156275, "y": 601.210204543441, "width": 30.00006103515625, "height": 29.33331298828125, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0GV", "roundness": {"type": 3}, "seed": 1494484335, "version": 212, "versionNonce": 2045847809, "isDeleted": false, "boundElements": [], "updated": 1749644557588, "link": null, "locked": false}, {"id": "C4aj3Eb0NFJixbc_XRbSw", "type": "rectangle", "x": 832.700498970797, "y": 601.9954262423616, "width": 30.00006103515625, "height": 29.33331298828125, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0Gl", "roundness": {"type": 3}, "seed": 735737121, "version": 263, "versionNonce": 1417741999, "isDeleted": false, "boundElements": [], "updated": 1749644561671, "link": null, "locked": false}, {"id": "5YMK6QRc9IlW6IPQoUn6N", "type": "rectangle", "x": 890.8078033195499, "y": 600.4249828445205, "width": 30.00006103515625, "height": 29.33331298828125, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0Gt", "roundness": {"type": 3}, "seed": 1469051023, "version": 310, "versionNonce": 1017541569, "isDeleted": false, "boundElements": [], "updated": 1749644564055, "link": null, "locked": false}, {"id": "PQZ9kYfrFbvSiRuVUekik", "type": "rectangle", "x": 949.7003293672234, "y": 600.4249828445205, "width": 30.00006103515625, "height": 29.33331298828125, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0Gx", "roundness": {"type": 3}, "seed": 672274401, "version": 340, "versionNonce": 1951597711, "isDeleted": false, "boundElements": [], "updated": 1749644569205, "link": null, "locked": false}, {"id": "EavdB7c7Laf5732e4fQCN", "type": "rectangle", "x": 1003.8814054042226, "y": 599.6397611456, "width": 30.00006103515625, "height": 29.33331298828125, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0Gz", "roundness": {"type": 3}, "seed": 38021743, "version": 372, "versionNonce": 1749234287, "isDeleted": false, "boundElements": [], "updated": 1749644569821, "link": null, "locked": false}, {"id": "eIPpaTl2uPBVhYKWZ3ZHp", "type": "rectangle", "x": 1386.0160358613978, "y": 242.79888418984132, "width": 377.9883694192478, "height": 52.47208813308461, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0T", "roundness": {"type": 3}, "seed": 118511969, "version": 337, "versionNonce": 1285954823, "isDeleted": false, "boundElements": [{"id": "BbbQ_hnM11wuRFaiYf-yK", "type": "arrow"}], "updated": 1750070009530, "link": null, "locked": false, "customData": {"legacyTextWrap": true}}, {"id": "K78bk8RZiCH42z9YDWOpi", "type": "rectangle", "x": 1726.664484371396, "y": 469.0985559491986, "width": 37.3333740234375, "height": 32.6666259765625, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0Y", "roundness": {"type": 3}, "seed": 866450785, "version": 216, "versionNonce": 1426452225, "isDeleted": false, "boundElements": [], "updated": 1749644737730, "link": null, "locked": false}, {"id": "ihBiRgF0", "type": "text", "x": 1721.9978583948334, "y": 512.4318689374799, "width": 48.974609375, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0Z", "roundness": null, "seed": 863978511, "version": 147, "versionNonce": 1196582625, "isDeleted": false, "boundElements": [], "updated": 1749644737730, "link": null, "locked": false, "text": "Mech", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Mech", "autoResize": true, "lineHeight": 1.25, "rawText": ""}, {"id": "08xTYV3p8Tr0_wyaa9rIU", "type": "rectangle", "x": 690.2537338636731, "y": 307.71790958093595, "width": 471.1402083813881, "height": 96.583736727327, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0f", "roundness": {"type": 3}, "seed": 17018159, "version": 126, "versionNonce": 417915591, "isDeleted": false, "boundElements": [{"id": "BbbQ_hnM11wuRFaiYf-yK", "type": "arrow"}], "updated": 1750070009529, "link": null, "locked": false}, {"id": "bvFqyxJ2", "type": "text", "x": 826.8843463674152, "y": 414.5096781656688, "width": 100.380859375, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0g", "roundness": null, "seed": 1571687777, "version": 67, "versionNonce": 820352576, "isDeleted": false, "boundElements": [], "updated": 1749898745335, "link": null, "locked": false, "text": "Export Mall", "rawText": "Export Mall", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Export Mall", "autoResize": true, "lineHeight": 1.25}, {"id": "GlLLp0TzgqLylXaUNO-iq", "type": "rectangle", "x": 726.3744412369099, "y": 324.2078048925694, "width": 32.979790623267036, "height": 29.053652174376452, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0h", "roundness": {"type": 3}, "seed": 2039964175, "version": 63, "versionNonce": 1951311759, "isDeleted": false, "boundElements": [], "updated": 1749644696862, "link": null, "locked": false}, {"text": "Import Mall", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "id": "OTWWXY00", "type": "text", "x": 1497.0812808082246, "y": 306.54004707826743, "width": 103.6328125, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "roundness": null, "seed": 79803, "version": 68, "versionNonce": 1700182703, "updated": 1749644793306, "isDeleted": false, "groupIds": [], "boundElements": [], "link": null, "locked": false, "containerId": null, "originalText": "Import Mall", "rawText": "Import Mall", "lineHeight": 1.25, "autoResize": true, "index": "b0i", "frameId": null}, {"id": "aVZwPUoLgm4zfeEgCir3n", "type": "arrow", "x": 726.6704973976578, "y": 563.1997038521038, "width": 0, "height": 131.3517151660625, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0j", "roundness": {"type": 2}, "seed": 750064999, "version": 73, "versionNonce": 1403398153, "isDeleted": false, "boundElements": null, "updated": 1750070001183, "link": null, "locked": false, "points": [[0, 0], [0, -131.3517151660625]], "lastCommittedPoint": null, "startBinding": {"elementId": "xa9_oOpOX008v3dCb5QaR", "focus": -0.8022951595956982, "gap": 19.220994655502636}, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "BbbQ_hnM11wuRFaiYf-yK", "type": "arrow", "x": 1174.5582766878824, "y": 347.1512739170647, "width": 195.95085381194122, "height": 77.51902661261067, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0k", "roundness": {"type": 2}, "seed": 1861067367, "version": 53, "versionNonce": 57602535, "isDeleted": false, "boundElements": null, "updated": 1750070009530, "link": null, "locked": false, "points": [[0, 0], [195.95085381194122, -77.51902661261067]], "lastCommittedPoint": null, "startBinding": {"elementId": "08xTYV3p8Tr0_wyaa9rIU", "focus": 0.6328748159409724, "gap": 13.1643344428212}, "endBinding": {"elementId": "eIPpaTl2uPBVhYKWZ3ZHp", "focus": 0.7950678061062826, "gap": 15.506905361574127}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "PE6S-V90SC1DZvYPD-B43", "type": "arrow", "x": 1614.5505278162655, "y": 350.74011799524766, "width": 96.89879011094035, "height": 100.48763418912313, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0l", "roundness": {"type": 2}, "seed": 1024528905, "version": 39, "versionNonce": 1417734759, "isDeleted": false, "boundElements": null, "updated": 1750070015606, "link": null, "locked": false, "points": [[0, 0], [96.89879011094035, 100.48763418912313]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}], "appState": {"theme": "light", "viewBackgroundColor": "#ffffff", "currentItemStrokeColor": "#1e1e1e", "currentItemBackgroundColor": "transparent", "currentItemFillStyle": "solid", "currentItemStrokeWidth": 2, "currentItemStrokeStyle": "solid", "currentItemRoughness": 1, "currentItemOpacity": 100, "currentItemFontFamily": 5, "currentItemFontSize": 20, "currentItemTextAlign": "left", "currentItemStartArrowhead": null, "currentItemEndArrowhead": "arrow", "currentItemArrowType": "round", "scrollX": -581.8606443191164, "scrollY": -78.76180845059417, "zoom": {"value": 1.114565}, "currentItemRoundness": "round", "gridSize": 20, "gridStep": 5, "gridModeEnabled": false, "gridColor": {"Bold": "rgba(217, 217, 217, 0.5)", "Regular": "rgba(230, 230, 230, 0.5)"}, "currentStrokeOptions": null, "frameRendering": {"enabled": true, "clip": true, "name": true, "outline": true}, "objectsSnapModeEnabled": false, "activeTool": {"type": "selection", "customType": null, "locked": false, "fromSelection": false, "lastActiveTool": null}}, "files": {}}